import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def read_and_analyze_excel():
    """读取并分析Excel文件"""
    try:
        # 读取Excel文件
        file_path = "作业登记表ABC栋202505.xlsx"
        df = pd.read_excel(file_path)
        
        print("=" * 60)
        print("Excel文件基本信息")
        print("=" * 60)
        print(f"文件路径: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列数: {len(df.columns)}")
        print(f"行数: {len(df)}")
        
        print("\n" + "=" * 60)
        print("列名信息")
        print("=" * 60)
        for i, col in enumerate(df.columns):
            print(f"第{i+1}列 ({chr(65+i)}列): {col}")
        
        print("\n" + "=" * 60)
        print("前5行数据预览")
        print("=" * 60)
        print(df.head())
        
        print("\n" + "=" * 60)
        print("数据类型信息")
        print("=" * 60)
        print(df.dtypes)
        
        # 查看K、L、N列的具体内容
        print("\n" + "=" * 60)
        print("关键列分析 (K、L、N列)")
        print("=" * 60)
        
        # 获取K、L、N列的索引
        k_col_idx = 10  # K列是第11列，索引为10
        l_col_idx = 11  # L列是第12列，索引为11
        n_col_idx = 13  # N列是第14列，索引为13
        
        if len(df.columns) > k_col_idx:
            k_col_name = df.columns[k_col_idx]
            print(f"K列 (第{k_col_idx+1}列): {k_col_name}")
            print("K列前10个值:")
            print(df.iloc[:10, k_col_idx])
            print()
        
        if len(df.columns) > l_col_idx:
            l_col_name = df.columns[l_col_idx]
            print(f"L列 (第{l_col_idx+1}列): {l_col_name}")
            print("L列前10个值:")
            print(df.iloc[:10, l_col_idx])
            print()
        
        if len(df.columns) > n_col_idx:
            n_col_name = df.columns[n_col_idx]
            print(f"N列 (第{n_col_idx+1}列): {n_col_name}")
            print("N列前10个值:")
            print(df.iloc[:10, n_col_idx])
            print()
        
        # 查看日期列
        print("\n" + "=" * 60)
        print("日期列分析")
        print("=" * 60)
        
        # 寻找可能的日期列
        for i, col in enumerate(df.columns):
            if '日期' in str(col) or 'date' in str(col).lower():
                print(f"发现日期列: 第{i+1}列 ({chr(65+i)}列): {col}")
                print("前10个值:")
                print(df.iloc[:10, i])
                print()
        
        # 查看第一列（通常是日期）
        if len(df.columns) > 0:
            print(f"第1列 (A列): {df.columns[0]}")
            print("前10个值:")
            print(df.iloc[:10, 0])
            print()
        
        print("\n" + "=" * 60)
        print("缺失值统计")
        print("=" * 60)
        missing_counts = df.isnull().sum()
        print(missing_counts[missing_counts > 0])
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

if __name__ == "__main__":
    df = read_and_analyze_excel()
