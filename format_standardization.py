import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def analyze_reference_format():
    """分析参考文件的格式"""
    print("="*60)
    print("分析参考文件格式: 作业登记表ABC栋202505.xlsx")
    print("="*60)
    
    try:
        ref_df = pd.read_excel("作业登记表ABC栋202505.xlsx")
        
        print(f"参考文件基本信息:")
        print(f"- 数据形状: {ref_df.shape}")
        print(f"- 列数: {len(ref_df.columns)}")
        
        print(f"\n参考文件列结构:")
        for i, col in enumerate(ref_df.columns):
            print(f"第{i+1}列 ({chr(65+i)}列): {col}")
        
        print(f"\n参考文件数据类型:")
        for i, (col, dtype) in enumerate(ref_df.dtypes.items()):
            print(f"第{i+1}列 {col}: {dtype}")
        
        print(f"\n参考文件前5行数据:")
        print(ref_df.head())
        
        # 分析关键列的格式
        print(f"\n关键列格式分析:")
        
        # 日期列格式
        date_col = ref_df.iloc[:10, 0]  # A列
        print(f"日期列样例: {list(date_col)}")
        
        # 时间列格式
        start_time_col = ref_df.iloc[:10, 10]  # K列
        end_time_col = ref_df.iloc[:10, 11]    # L列
        duration_col = ref_df.iloc[:10, 13]    # N列
        
        print(f"实际开始时间样例: {list(start_time_col)}")
        print(f"实际结束时间样例: {list(end_time_col)}")
        print(f"实际作业时长样例: {list(duration_col)}")
        
        return ref_df
        
    except Exception as e:
        print(f"分析参考文件时出错: {e}")
        return None

def standardize_file_format(file_path, reference_df):
    """将文件格式标准化为参考格式"""
    print(f"\n{'='*60}")
    print(f"标准化文件格式: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取目标文件
        df = pd.read_excel(file_path)
        
        print(f"原文件信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 列数: {len(df.columns)}")
        
        # 确保列结构与参考文件一致
        if len(df.columns) != len(reference_df.columns):
            print(f"警告: 列数不匹配 - 目标文件:{len(df.columns)}, 参考文件:{len(reference_df.columns)}")
        
        # 确保列名与参考文件一致
        df.columns = reference_df.columns
        print(f"✅ 列名已标准化")
        
        # 标准化数据格式
        standardized_df = df.copy()
        
        # 1. 标准化日期格式 (A列)
        date_col_idx = 0
        for i in range(len(standardized_df)):
            date_val = str(standardized_df.iloc[i, date_col_idx]).strip()
            if date_val != 'nan' and date_val != '':
                # 确保日期格式为 "M/D" 格式
                if '/' in date_val:
                    parts = date_val.split('/')
                    if len(parts) >= 2:
                        month = int(parts[0])
                        day = int(parts[1])
                        standardized_df.iloc[i, date_col_idx] = f"{month}/{day}"
        
        # 2. 标准化时间格式 (K列、L列)
        time_columns = [10, 11]  # K列和L列
        for col_idx in time_columns:
            for i in range(len(standardized_df)):
                time_val = str(standardized_df.iloc[i, col_idx]).strip()
                if time_val != 'nan' and time_val != '' and ':' in time_val:
                    try:
                        # 解析时间并标准化为 HH:MM:SS 格式
                        if time_val.count(':') == 1:  # HH:MM
                            time_obj = datetime.strptime(time_val, '%H:%M')
                            standardized_df.iloc[i, col_idx] = time_obj.strftime('%H:%M:%S')
                        elif time_val.count(':') == 2:  # HH:MM:SS
                            time_obj = datetime.strptime(time_val, '%H:%M:%S')
                            standardized_df.iloc[i, col_idx] = time_obj.strftime('%H:%M:%S')
                    except:
                        pass  # 保持原值
        
        # 3. 标准化作业时长格式 (N列)
        duration_col_idx = 13
        for i in range(len(standardized_df)):
            duration_val = str(standardized_df.iloc[i, duration_col_idx]).strip()
            if duration_val != 'nan' and duration_val != '' and ':' in duration_val:
                try:
                    # 确保格式为 H:MM 或 HH:MM
                    parts = duration_val.split(':')
                    if len(parts) >= 2:
                        hours = int(parts[0])
                        minutes = int(parts[1])
                        if hours == 0:
                            standardized_df.iloc[i, duration_col_idx] = f"0:{minutes:02d}"
                        else:
                            standardized_df.iloc[i, duration_col_idx] = f"{hours}:{minutes:02d}"
                except:
                    pass  # 保持原值
        
        # 4. 确保数据类型与参考文件一致
        for col_name in standardized_df.columns:
            if col_name in reference_df.columns:
                ref_dtype = reference_df[col_name].dtype
                if ref_dtype == 'object':
                    standardized_df[col_name] = standardized_df[col_name].astype('object')
                elif ref_dtype == 'int64':
                    try:
                        standardized_df[col_name] = pd.to_numeric(standardized_df[col_name], errors='coerce').fillna(0).astype('int64')
                    except:
                        pass
        
        print(f"✅ 数据格式已标准化")
        
        # 显示标准化后的样例
        print(f"\n标准化后前5行数据:")
        print(standardized_df.head())
        
        return standardized_df
        
    except Exception as e:
        print(f"标准化文件时出错: {e}")
        return None

def compare_formats(ref_df, target_df, file_name):
    """比较格式是否一致"""
    print(f"\n{'='*60}")
    print(f"格式对比: {file_name}")
    print(f"{'='*60}")
    
    # 比较列名
    ref_columns = list(ref_df.columns)
    target_columns = list(target_df.columns)
    
    print(f"列名对比:")
    columns_match = ref_columns == target_columns
    print(f"- 列名是否一致: {'✅' if columns_match else '❌'}")
    
    if not columns_match:
        print(f"- 参考文件列名: {ref_columns}")
        print(f"- 目标文件列名: {target_columns}")
    
    # 比较数据类型
    print(f"\n数据类型对比:")
    dtype_issues = []
    for col in ref_df.columns:
        if col in target_df.columns:
            ref_dtype = ref_df[col].dtype
            target_dtype = target_df[col].dtype
            if ref_dtype != target_dtype:
                dtype_issues.append(f"  {col}: 参考({ref_dtype}) vs 目标({target_dtype})")
    
    if dtype_issues:
        print(f"- 数据类型差异:")
        for issue in dtype_issues:
            print(issue)
    else:
        print(f"- 数据类型: ✅ 一致")
    
    # 比较关键列格式
    print(f"\n关键列格式对比:")
    
    # 日期格式
    ref_dates = ref_df.iloc[:5, 0].tolist()
    target_dates = target_df.iloc[:5, 0].tolist()
    print(f"- 日期格式 (前5行):")
    print(f"  参考: {ref_dates}")
    print(f"  目标: {target_dates}")
    
    # 时间格式
    ref_start_times = ref_df.iloc[:5, 10].tolist()
    target_start_times = target_df.iloc[:5, 10].tolist()
    print(f"- 开始时间格式 (前5行):")
    print(f"  参考: {ref_start_times}")
    print(f"  目标: {target_start_times}")
    
    # 作业时长格式
    ref_durations = ref_df.iloc[:5, 13].tolist()
    target_durations = target_df.iloc[:5, 13].tolist()
    print(f"- 作业时长格式 (前5行):")
    print(f"  参考: {ref_durations}")
    print(f"  目标: {target_durations}")

def main():
    """主函数"""
    # 1. 分析参考文件格式
    reference_df = analyze_reference_format()
    
    if reference_df is None:
        print("❌ 无法读取参考文件")
        return
    
    # 2. 处理目标文件
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 标准化格式
        standardized_df = standardize_file_format(file_path, reference_df)
        
        if standardized_df is not None:
            # 保存标准化后的文件
            output_path = file_path.replace('.xlsx', '_formatted.xlsx')
            standardized_df.to_excel(output_path, index=False)
            print(f"✅ 已保存标准化文件: {output_path}")
            
            # 格式对比
            compare_formats(reference_df, standardized_df, file_path)
            
            # 覆盖原文件
            standardized_df.to_excel(file_path, index=False)
            print(f"✅ 已更新原文件: {file_path}")
        else:
            print(f"❌ 文件处理失败: {file_path}")

if __name__ == "__main__":
    main()
