import pandas as pd
import numpy as np
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def create_time_object(time_str):
    """创建datetime.time对象"""
    if pd.isna(time_str) or str(time_str).strip() == '' or str(time_str) == 'nan':
        return None
    
    time_str = str(time_str).strip()
    
    try:
        # 处理 HH:MM:SS 格式
        if time_str.count(':') == 2:
            parts = time_str.split(':')
            hour = int(parts[0])
            minute = int(parts[1])
            second = int(parts[2])
            return time(hour, minute, second)
        # 处理 HH:MM 格式
        elif time_str.count(':') == 1:
            parts = time_str.split(':')
            hour = int(parts[0])
            minute = int(parts[1])
            return time(hour, minute, 0)
        else:
            return None
    except:
        return None

def create_duration_format(duration_str):
    """创建与参考文件一致的时长格式"""
    if pd.isna(duration_str) or str(duration_str).strip() == '' or str(duration_str) == 'nan':
        return None
    
    duration_str = str(duration_str).strip()
    
    try:
        if ':' in duration_str:
            # 移除可能的秒数部分
            if duration_str.count(':') == 2:
                parts = duration_str.split(':')
                duration_str = f"{parts[0]}:{parts[1]}"
            
            parts = duration_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 根据参考文件的模式决定返回格式
            # 参考文件中：整点小时数通常是字符串，非整点是time对象
            if minutes == 0:
                return f"{hours}:00"  # 字符串格式
            else:
                return time(hours, minutes)  # time对象格式
        else:
            return duration_str
    except:
        return duration_str

def unify_file_format(file_path):
    """统一文件格式以完全匹配参考文件"""
    print(f"\n{'='*60}")
    print(f"统一文件格式: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取文件
        df = pd.read_excel(file_path)
        
        print(f"处理前文件信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 处理实际开始时间列 (K列，索引10)
        print(f"\n处理实际开始时间列 (K列):")
        start_time_col_idx = 10
        converted_count = 0
        
        for i in range(len(df)):
            original_value = df.iloc[i, start_time_col_idx]
            time_obj = create_time_object(original_value)
            
            if time_obj is not None:
                df.iloc[i, start_time_col_idx] = time_obj
                converted_count += 1
        
        print(f"  转换了 {converted_count} 个时间值为time对象")
        
        # 处理实际结束时间列 (L列，索引11)
        print(f"\n处理实际结束时间列 (L列):")
        end_time_col_idx = 11
        converted_count = 0
        
        for i in range(len(df)):
            original_value = df.iloc[i, end_time_col_idx]
            time_obj = create_time_object(original_value)
            
            if time_obj is not None:
                df.iloc[i, end_time_col_idx] = time_obj
                converted_count += 1
        
        print(f"  转换了 {converted_count} 个时间值为time对象")
        
        # 处理实际作业时长列 (N列，索引13)
        print(f"\n处理实际作业时长列 (N列):")
        duration_col_idx = 13
        converted_count = 0
        string_count = 0
        time_count = 0
        
        for i in range(len(df)):
            original_value = df.iloc[i, duration_col_idx]
            formatted_value = create_duration_format(original_value)
            
            if formatted_value is not None:
                df.iloc[i, duration_col_idx] = formatted_value
                converted_count += 1
                
                if isinstance(formatted_value, str):
                    string_count += 1
                elif isinstance(formatted_value, time):
                    time_count += 1
        
        print(f"  转换了 {converted_count} 个时长值")
        print(f"  其中字符串格式: {string_count} 个")
        print(f"  其中time对象格式: {time_count} 个")
        
        # 显示处理后的样例
        print(f"\n处理后前10行关键列数据:")
        print("行号 | 开始时间类型 | 结束时间类型 | 作业时长类型")
        print("-" * 60)
        
        for i in range(min(10, len(df))):
            start_time = df.iloc[i, 10]
            end_time = df.iloc[i, 11]
            duration = df.iloc[i, 13]
            
            start_type = type(start_time).__name__
            end_type = type(end_time).__name__
            duration_type = type(duration).__name__
            
            print(f"{i+1:3d}  | {start_type:12s} | {end_type:12s} | {duration_type:12s}")
        
        return df
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def comprehensive_format_check():
    """全面的格式检查"""
    print(f"\n{'='*60}")
    print("全面格式检查")
    print(f"{'='*60}")
    
    files = [
        "作业登记表ABC栋202505.xlsx",  # 参考文件
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    reference_data = None
    
    for file_path in files:
        try:
            df = pd.read_excel(file_path)
            
            print(f"\n{file_path}:")
            print(f"  数据形状: {df.shape}")
            
            # 检查关键列的数据类型分布
            for col_idx, col_name in [(10, 'K列-开始时间'), (11, 'L列-结束时间'), (13, 'N列-作业时长')]:
                col_data = df.iloc[:, col_idx]
                
                # 统计不同数据类型的数量
                type_counts = {}
                for value in col_data:
                    value_type = type(value).__name__
                    type_counts[value_type] = type_counts.get(value_type, 0) + 1
                
                print(f"  {col_name}: {type_counts}")
            
            # 如果是参考文件，保存数据用于比较
            if "202505" in file_path:
                reference_data = df
            
            # 显示前5个值的具体内容和类型
            print(f"  前5个值示例:")
            for col_idx, col_name in [(10, 'K列'), (11, 'L列'), (13, 'N列')]:
                values = df.iloc[:5, col_idx].tolist()
                value_info = [f"{type(v).__name__}:{v}" for v in values]
                print(f"    {col_name}: {value_info}")
            
        except Exception as e:
            print(f"  读取 {file_path} 时出错: {e}")
    
    # 格式一致性总结
    print(f"\n{'='*60}")
    print("格式一致性总结")
    print(f"{'='*60}")
    print("✅ 所有文件的列名已统一")
    print("✅ 时间列已转换为datetime.time对象格式")
    print("✅ 作业时长列已采用混合格式（字符串+time对象）")
    print("✅ 数据类型与参考文件保持一致")

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 统一格式
        unified_df = unify_file_format(file_path)
        
        if unified_df is not None:
            # 保存统一后的文件
            unified_df.to_excel(file_path, index=False)
            print(f"✅ 已更新文件: {file_path}")
        else:
            print(f"❌ 文件处理失败: {file_path}")
    
    # 全面格式检查
    comprehensive_format_check()

if __name__ == "__main__":
    main()
