import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
from openpyxl import load_workbook
import warnings
warnings.filterwarnings('ignore')

def parse_time_string(time_str):
    """解析时间字符串，返回datetime对象"""
    if pd.isna(time_str) or str(time_str).strip() == '' or str(time_str) == 'nan':
        return None
    
    try:
        time_str = str(time_str).strip()
        
        # 如果是time对象，转换为datetime
        if isinstance(time_str, time):
            return datetime.combine(datetime.today(), time_str)
        
        # 处理字符串格式
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) >= 2:
                hour = int(parts[0])
                minute = int(parts[1])
                second = int(parts[2]) if len(parts) > 2 else 0
                return datetime.combine(datetime.today(), time(hour, minute, second))
        
        return None
        
    except Exception as e:
        print(f"解析时间出错 '{time_str}': {e}")
        return None

def parse_duration_string(duration_str):
    """解析时长字符串，返回总分钟数"""
    if pd.isna(duration_str) or str(duration_str).strip() == '' or str(duration_str) == 'nan':
        return 0
    
    try:
        duration_str = str(duration_str).strip()
        
        # 如果是time对象
        if isinstance(duration_str, time):
            return duration_str.hour * 60 + duration_str.minute
        
        # 如果是字符串格式 h:mm
        if ':' in duration_str:
            parts = duration_str.split(':')
            if len(parts) >= 2:
                hours = int(parts[0])
                minutes = int(parts[1])
                return hours * 60 + minutes
        
        return 0
        
    except Exception as e:
        print(f"解析时长出错 '{duration_str}': {e}")
        return 0

def calculate_overlap_minutes(work_start, work_end, break_start, break_end):
    """计算工作时间与休息时间的重叠分钟数"""
    if work_start is None or work_end is None:
        return 0
    
    try:
        # 创建休息时间的datetime对象
        break_start_dt = datetime.combine(datetime.today(), break_start)
        break_end_dt = datetime.combine(datetime.today(), break_end)
        
        # 计算重叠时间
        overlap_start = max(work_start, break_start_dt)
        overlap_end = min(work_end, break_end_dt)
        
        if overlap_start < overlap_end:
            overlap_duration = overlap_end - overlap_start
            return int(overlap_duration.total_seconds() / 60)
        
        return 0
        
    except Exception as e:
        print(f"计算重叠时间出错: {e}")
        return 0

def format_minutes_to_hmm(total_minutes):
    """将总分钟数格式化为h:mm格式"""
    if total_minutes <= 0:
        return "0:00"
    
    hours = total_minutes // 60
    minutes = total_minutes % 60
    return f"{hours}:{minutes:02d}"

def process_additional_breaks(file_path):
    """处理额外的休息时间"""
    print(f"\n{'='*60}")
    print(f"处理文件: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 总行数: {len(df)}")
        
        # 定义新增的休息时间段
        additional_breaks = [
            (time(10, 0), time(10, 10), "上午休息"),    # 10:00-10:10
            (time(15, 0), time(15, 10), "下午休息"),    # 15:00-15:10
            (time(17, 30), time(18, 0), "晚餐时间")     # 17:30-18:00
        ]
        
        print(f"\n需要扣除的额外休息时间:")
        for break_start, break_end, break_name in additional_breaks:
            print(f"- {break_name}: {break_start.strftime('%H:%M')}~{break_end.strftime('%H:%M')}")
        
        # 找到关键列
        k_col_idx = 10  # K列 - 实际开始时间
        l_col_idx = 11  # L列 - 实际结束时间  
        n_col_idx = 13  # N列 - 实际作业时长（不含午休时长）
        
        k_col_name = df.columns[k_col_idx] if k_col_idx < len(df.columns) else "未找到"
        l_col_name = df.columns[l_col_idx] if l_col_idx < len(df.columns) else "未找到"
        n_col_name = df.columns[n_col_idx] if n_col_idx < len(df.columns) else "未找到"
        
        print(f"\n关键列信息:")
        print(f"- K列 (第{k_col_idx+1}列): {k_col_name}")
        print(f"- L列 (第{l_col_idx+1}列): {l_col_name}")
        print(f"- N列 (第{n_col_idx+1}列): {n_col_name}")
        
        # 处理每一行数据
        processed_count = 0
        total_deducted_minutes = 0
        deduction_details = []
        
        for i in range(len(df)):
            # 获取开始和结束时间
            start_time_str = df.iloc[i, k_col_idx]
            end_time_str = df.iloc[i, l_col_idx]
            current_duration_str = df.iloc[i, n_col_idx]
            
            # 解析时间
            start_time = parse_time_string(start_time_str)
            end_time = parse_time_string(end_time_str)
            current_duration_minutes = parse_duration_string(current_duration_str)
            
            if start_time and end_time and current_duration_minutes > 0:
                # 计算与额外休息时间的重叠
                total_overlap_minutes = 0
                row_deductions = []
                
                for break_start, break_end, break_name in additional_breaks:
                    overlap_minutes = calculate_overlap_minutes(start_time, end_time, break_start, break_end)
                    if overlap_minutes > 0:
                        total_overlap_minutes += overlap_minutes
                        row_deductions.append(f"{break_name}:{overlap_minutes}分钟")
                
                # 计算新的作业时长
                new_duration_minutes = current_duration_minutes - total_overlap_minutes
                new_duration_minutes = max(0, new_duration_minutes)  # 确保不为负数
                
                # 格式化为h:mm
                new_duration_str = format_minutes_to_hmm(new_duration_minutes)
                
                # 更新N列
                df.iloc[i, n_col_idx] = new_duration_str
                
                if total_overlap_minutes > 0:
                    processed_count += 1
                    total_deducted_minutes += total_overlap_minutes
                    
                    deduction_details.append({
                        'row': i + 2,  # Excel行号
                        'start_time': start_time_str,
                        'end_time': end_time_str,
                        'original_duration': current_duration_str,
                        'new_duration': new_duration_str,
                        'deducted_minutes': total_overlap_minutes,
                        'deductions': row_deductions
                    })
        
        print(f"\n处理结果:")
        print(f"- 受影响的行数: {processed_count}")
        print(f"- 总扣除时间: {total_deducted_minutes} 分钟")
        print(f"- 平均每行扣除: {total_deducted_minutes/processed_count:.1f} 分钟" if processed_count > 0 else "- 平均每行扣除: 0 分钟")
        
        # 显示前10个处理案例
        if deduction_details:
            print(f"\n前10个处理案例:")
            print("行号 | 开始时间 | 结束时间 | 原时长 | 新时长 | 扣除 | 详情")
            print("-" * 80)
            
            for detail in deduction_details[:10]:
                deductions_str = ", ".join(detail['deductions'])
                print(f"{detail['row']:4d} | {str(detail['start_time']):8s} | {str(detail['end_time']):8s} | {str(detail['original_duration']):6s} | {str(detail['new_duration']):6s} | {detail['deducted_minutes']:4d} | {deductions_str}")
        
        return df, processed_count, total_deducted_minutes
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None, 0, 0

def verify_processing_results(df, file_path):
    """验证处理结果"""
    print(f"\n{'='*60}")
    print(f"验证处理结果: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 统计N列的数据
        n_col_data = df.iloc[:, 13]  # N列
        
        # 统计时长分布
        duration_stats = {}
        for duration in n_col_data:
            if pd.notna(duration) and str(duration) != 'nan':
                duration_str = str(duration)
                if ':' in duration_str:
                    parts = duration_str.split(':')
                    if len(parts) >= 2:
                        hours = int(parts[0])
                        if hours not in duration_stats:
                            duration_stats[hours] = 0
                        duration_stats[hours] += 1
        
        print(f"N列时长分布 (按小时):")
        for hour in sorted(duration_stats.keys()):
            print(f"- {hour}小时段: {duration_stats[hour]} 条记录")
        
        # 检查格式一致性
        format_check = True
        invalid_formats = []
        
        for i, duration in enumerate(n_col_data):
            if pd.notna(duration) and str(duration) != 'nan':
                duration_str = str(duration)
                if ':' not in duration_str:
                    format_check = False
                    invalid_formats.append(f"行{i+2}: {duration_str}")
        
        print(f"\n格式检查:")
        print(f"- 格式一致性: {'✅ 正确' if format_check else '❌ 有问题'}")
        
        if invalid_formats:
            print(f"- 格式问题: {invalid_formats[:5]}{'...' if len(invalid_formats) > 5 else ''}")
        
        return format_check
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return False

def main():
    """主函数"""
    file_path = "作业登记表ABC栋202505~07.xlsx"
    
    print("="*80)
    print("处理额外休息时间 - 扣除上午休息、下午休息、晚餐时间")
    print("="*80)
    
    # 处理文件
    processed_df, affected_rows, total_deducted = process_additional_breaks(file_path)
    
    if processed_df is not None:
        # 验证结果
        is_valid = verify_processing_results(processed_df, file_path)
        
        if is_valid:
            # 保存处理后的文件
            output_path = file_path.replace('.xlsx', '_processed.xlsx')
            processed_df.to_excel(output_path, index=False)
            print(f"\n✅ 处理完成，已保存到: {output_path}")
            
            # 覆盖原文件
            processed_df.to_excel(file_path, index=False)
            print(f"✅ 已更新原文件: {file_path}")
            
            # 总结
            print(f"\n{'='*60}")
            print("处理完成总结")
            print(f"{'='*60}")
            print(f"✅ 完成的工作:")
            print(f"  1. 扣除了上午休息时间 (10:00~10:10)")
            print(f"  2. 扣除了下午休息时间 (15:00~15:10)")
            print(f"  3. 扣除了晚餐时间 (17:30~18:00)")
            print(f"  4. 保持了h:mm时间格式")
            
            print(f"\n📊 处理统计:")
            print(f"  - 受影响行数: {affected_rows}")
            print(f"  - 总扣除时间: {total_deducted} 分钟")
            print(f"  - 文件状态: ✅ 处理完成")
            
        else:
            print(f"\n❌ 处理结果验证失败，请检查数据")
    else:
        print(f"\n❌ 文件处理失败")

if __name__ == "__main__":
    main()
