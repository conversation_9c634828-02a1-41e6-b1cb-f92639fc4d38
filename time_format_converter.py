import pandas as pd
import numpy as np
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def convert_time_to_hmm_format(time_value):
    """将时间值转换为h:mm格式"""
    if pd.isna(time_value) or str(time_value).strip() == '' or str(time_value) == 'nan':
        return None
    
    try:
        # 如果是datetime.time对象
        if isinstance(time_value, time):
            hour = time_value.hour
            minute = time_value.minute
            return f"{hour}:{minute:02d}"
        
        # 如果是字符串
        time_str = str(time_value).strip()
        
        # 处理各种时间格式
        if ':' in time_str:
            # 移除可能的日期部分（如果存在）
            if ' ' in time_str:
                time_str = time_str.split(' ')[-1]  # 取最后一部分作为时间
            
            parts = time_str.split(':')
            if len(parts) >= 2:
                hour = int(parts[0])
                minute = int(parts[1])
                # 忽略秒数部分
                return f"{hour}:{minute:02d}"
        
        # 如果是纯数字格式（如1430表示14:30）
        elif time_str.isdigit() and len(time_str) == 4:
            hour = int(time_str[:2])
            minute = int(time_str[2:])
            return f"{hour}:{minute:02d}"
        
        return time_str  # 如果无法解析，返回原值
        
    except Exception as e:
        print(f"转换时间格式时出错 '{time_value}': {e}")
        return str(time_value)

def convert_duration_to_hmm_format(duration_value):
    """将时长值转换为h:mm格式"""
    if pd.isna(duration_value) or str(duration_value).strip() == '' or str(duration_value) == 'nan':
        return None
    
    try:
        # 如果是datetime.time对象
        if isinstance(duration_value, time):
            hour = duration_value.hour
            minute = duration_value.minute
            return f"{hour}:{minute:02d}"
        
        # 如果是字符串
        duration_str = str(duration_value).strip()
        
        if ':' in duration_str:
            parts = duration_str.split(':')
            if len(parts) >= 2:
                hour = int(parts[0])
                minute = int(parts[1])
                # 忽略秒数部分
                return f"{hour}:{minute:02d}"
        
        return duration_str  # 如果无法解析，返回原值
        
    except Exception as e:
        print(f"转换时长格式时出错 '{duration_value}': {e}")
        return str(duration_value)

def process_time_format_in_file(file_path):
    """处理文件中的时间格式"""
    print(f"\n{'='*60}")
    print(f"处理文件: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 列数: {len(df.columns)}")
        
        # 找到需要处理的时间列
        time_columns = []
        
        # 查找包含时间的列
        for i, col_name in enumerate(df.columns):
            col_name_str = str(col_name).lower()
            if any(keyword in col_name_str for keyword in ['时间', 'time', '时长']):
                time_columns.append((i, col_name))
        
        print(f"\n发现的时间相关列:")
        for col_idx, col_name in time_columns:
            print(f"  第{col_idx+1}列 ({chr(65+col_idx)}列): {col_name}")
        
        # 处理每个时间列
        total_converted = 0
        
        for col_idx, col_name in time_columns:
            print(f"\n处理 {col_name} (第{col_idx+1}列):")
            
            converted_count = 0
            error_count = 0
            
            # 显示处理前的样例
            print(f"  处理前样例 (前5个值):")
            for i in range(min(5, len(df))):
                original_value = df.iloc[i, col_idx]
                print(f"    行{i+1}: {original_value} (类型: {type(original_value).__name__})")
            
            # 转换时间格式
            for i in range(len(df)):
                original_value = df.iloc[i, col_idx]
                
                # 根据列名选择转换函数
                if '时长' in col_name:
                    converted_value = convert_duration_to_hmm_format(original_value)
                else:
                    converted_value = convert_time_to_hmm_format(original_value)
                
                if converted_value is not None:
                    df.iloc[i, col_idx] = converted_value
                    converted_count += 1
                else:
                    error_count += 1
            
            print(f"  处理结果:")
            print(f"    成功转换: {converted_count} 个")
            print(f"    转换失败: {error_count} 个")
            
            # 显示处理后的样例
            print(f"  处理后样例 (前5个值):")
            for i in range(min(5, len(df))):
                new_value = df.iloc[i, col_idx]
                print(f"    行{i+1}: {new_value}")
            
            total_converted += converted_count
        
        print(f"\n总计转换了 {total_converted} 个时间值")
        
        return df
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def verify_time_format(file_path):
    """验证时间格式转换结果"""
    print(f"\n{'='*60}")
    print(f"验证时间格式: {file_path}")
    print(f"{'='*60}")
    
    try:
        df = pd.read_excel(file_path)
        
        # 检查关键时间列
        time_columns = [
            (2, "起始时间"),
            (3, "截止时间"), 
            (4, "作业时长"),
            (10, "实际开始时间"),
            (11, "实际结束时间"),
            (12, "实际作业时长"),
            (13, "实际作业时长（不含午休时长）")
        ]
        
        print("时间格式验证结果:")
        print("列名 | 样例值 | 格式检查")
        print("-" * 50)
        
        for col_idx, col_name in time_columns:
            if col_idx < len(df.columns):
                # 取前3个非空值作为样例
                sample_values = []
                for i in range(len(df)):
                    value = df.iloc[i, col_idx]
                    if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                        sample_values.append(str(value))
                        if len(sample_values) >= 3:
                            break
                
                # 检查格式是否符合h:mm
                format_ok = True
                for value in sample_values:
                    if ':' in value:
                        parts = value.split(':')
                        if len(parts) > 2:  # 如果有秒数部分
                            format_ok = False
                            break
                    
                status = "✅" if format_ok else "❌"
                sample_str = ", ".join(sample_values[:3])
                print(f"{col_name[:15]:15s} | {sample_str[:20]:20s} | {status}")
        
        # 统计时间格式
        print(f"\n格式统计:")
        hmm_count = 0
        hmmss_count = 0
        other_count = 0
        
        for col_idx, col_name in time_columns:
            if col_idx < len(df.columns):
                for i in range(len(df)):
                    value = str(df.iloc[i, col_idx])
                    if ':' in value and value != 'nan':
                        parts = value.split(':')
                        if len(parts) == 2:
                            hmm_count += 1
                        elif len(parts) == 3:
                            hmmss_count += 1
                        else:
                            other_count += 1
        
        print(f"  h:mm 格式: {hmm_count} 个")
        print(f"  h:mm:ss 格式: {hmmss_count} 个")
        print(f"  其他格式: {other_count} 个")
        
        success_rate = hmm_count / (hmm_count + hmmss_count + other_count) * 100 if (hmm_count + hmmss_count + other_count) > 0 else 0
        print(f"  h:mm 格式占比: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"验证文件时出错: {e}")

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 处理时间格式
        processed_df = process_time_format_in_file(file_path)
        
        if processed_df is not None:
            # 保存处理后的文件
            output_path = file_path.replace('.xlsx', '_time_formatted.xlsx')
            processed_df.to_excel(output_path, index=False)
            print(f"✅ 已保存格式化文件: {output_path}")
            
            # 覆盖原文件
            processed_df.to_excel(file_path, index=False)
            print(f"✅ 已更新原文件: {file_path}")
            
            # 验证格式
            verify_time_format(file_path)
        else:
            print(f"❌ 文件处理失败: {file_path}")

if __name__ == "__main__":
    main()
