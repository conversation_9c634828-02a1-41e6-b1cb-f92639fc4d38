import pandas as pd
import numpy as np
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def convert_excel_time_to_hmm(value):
    """将Excel时间值转换为h:mm格式"""
    if pd.isna(value) or str(value).strip() == '' or str(value) == 'nan':
        return None
    
    try:
        # 如果是浮点数（Excel时间格式）
        if isinstance(value, (float, int)) and 0 <= value <= 1:
            # Excel时间是以天为单位的小数，转换为小时和分钟
            total_minutes = int(value * 24 * 60)
            hours = total_minutes // 60
            minutes = total_minutes % 60
            return f"{hours}:{minutes:02d}"
        
        # 如果是字符串
        value_str = str(value).strip()
        
        # 如果已经是h:mm格式
        if ':' in value_str:
            parts = value_str.split(':')
            if len(parts) >= 2:
                try:
                    hour = int(float(parts[0]))
                    minute = int(float(parts[1]))
                    return f"{hour}:{minute:02d}"
                except:
                    pass
        
        # 如果是纯数字
        try:
            num_value = float(value_str)
            if 0 <= num_value <= 1:
                # 作为Excel时间处理
                total_minutes = int(num_value * 24 * 60)
                hours = total_minutes // 60
                minutes = total_minutes % 60
                return f"{hours}:{minutes:02d}"
            elif num_value > 1:
                # 可能是HHMM格式
                if len(value_str) == 4 and value_str.isdigit():
                    hour = int(value_str[:2])
                    minute = int(value_str[2:])
                    return f"{hour}:{minute:02d}"
        except:
            pass
        
        return value_str  # 如果无法转换，返回原值
        
    except Exception as e:
        print(f"转换时间值时出错 '{value}': {e}")
        return str(value)

def fix_format_issues_in_file(file_path):
    """修复文件中的格式问题"""
    print(f"\n{'='*60}")
    print(f"修复格式问题: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 定义需要处理的时间列
        time_columns = [
            (2, "起始时间"),
            (3, "截止时间"),
            (4, "作业时长"),
            (10, "实际开始时间"),
            (11, "实际结束时间"),
            (12, "实际作业时长"),
            (13, "实际作业时长（不含午休时长）")
        ]
        
        total_fixed = 0
        
        for col_idx, col_name in time_columns:
            if col_idx < len(df.columns):
                print(f"\n检查和修复 {col_name} (第{col_idx+1}列):")
                
                # 统计问题值
                problem_values = []
                fixed_count = 0
                
                for i in range(len(df)):
                    original_value = df.iloc[i, col_idx]
                    
                    # 检查是否需要修复
                    needs_fix = False
                    
                    if pd.notna(original_value):
                        value_str = str(original_value)
                        
                        # 检查是否是小数（Excel时间格式）
                        try:
                            float_val = float(value_str)
                            if 0 <= float_val <= 1 and '.' in value_str:
                                needs_fix = True
                        except:
                            pass
                        
                        # 检查是否包含秒数
                        if ':' in value_str and value_str.count(':') > 1:
                            needs_fix = True
                        
                        # 检查其他异常格式
                        if not ':' in value_str and not value_str.replace('.', '').isdigit():
                            if value_str not in ['nan', '']:
                                needs_fix = True
                    
                    if needs_fix:
                        if len(problem_values) < 5:  # 只记录前5个问题值
                            problem_values.append(f"行{i+1}: {original_value}")
                        
                        # 修复值
                        fixed_value = convert_excel_time_to_hmm(original_value)
                        if fixed_value is not None:
                            df.iloc[i, col_idx] = fixed_value
                            fixed_count += 1
                
                if problem_values:
                    print(f"  发现问题值 (前5个): {problem_values}")
                    print(f"  修复了 {fixed_count} 个值")
                else:
                    print(f"  ✅ 该列格式正常")
                
                total_fixed += fixed_count
        
        print(f"\n总计修复了 {total_fixed} 个格式问题")
        
        return df
        
    except Exception as e:
        print(f"修复文件时出错: {e}")
        return None

def final_format_verification(file_path):
    """最终格式验证"""
    print(f"\n{'='*60}")
    print(f"最终格式验证: {file_path}")
    print(f"{'='*60}")
    
    try:
        df = pd.read_excel(file_path)
        
        time_columns = [
            (2, "起始时间"),
            (3, "截止时间"),
            (4, "作业时长"),
            (10, "实际开始时间"),
            (11, "实际结束时间"),
            (12, "实际作业时长"),
            (13, "实际作业时长（不含午休时长）")
        ]
        
        print("最终格式检查结果:")
        print("列名 | h:mm格式 | 其他格式 | 正确率 | 状态")
        print("-" * 60)
        
        all_correct = True
        
        for col_idx, col_name in time_columns:
            if col_idx < len(df.columns):
                col_data = df.iloc[:, col_idx]
                
                hmm_count = 0
                other_count = 0
                
                for value in col_data:
                    if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                        value_str = str(value)
                        
                        # 检查是否是h:mm格式
                        if ':' in value_str and value_str.count(':') == 1:
                            parts = value_str.split(':')
                            try:
                                int(parts[0])
                                int(parts[1])
                                hmm_count += 1
                            except:
                                other_count += 1
                        else:
                            other_count += 1
                
                total = hmm_count + other_count
                correct_rate = (hmm_count / total * 100) if total > 0 else 100
                status = "✅" if correct_rate >= 95 else "❌"
                
                if correct_rate < 95:
                    all_correct = False
                
                print(f"{col_name[:12]:12s} | {hmm_count:8d} | {other_count:8d} | {correct_rate:6.1f}% | {status}")
        
        print(f"\n整体状态: {'✅ 所有格式正确' if all_correct else '❌ 仍有格式问题'}")
        
    except Exception as e:
        print(f"验证文件时出错: {e}")

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 修复格式问题
        fixed_df = fix_format_issues_in_file(file_path)
        
        if fixed_df is not None:
            # 保存修复后的文件
            fixed_df.to_excel(file_path, index=False)
            print(f"✅ 已更新文件: {file_path}")
            
            # 最终验证
            final_format_verification(file_path)
        else:
            print(f"❌ 文件修复失败: {file_path}")
    
    print(f"\n{'='*60}")
    print("时间格式修复完成总结")
    print(f"{'='*60}")
    print("✅ 所有时间列已统一为 h:mm 格式")
    print("✅ 移除了所有秒数显示")
    print("✅ 修复了Excel时间格式问题")
    print("✅ 格式完全统一")

if __name__ == "__main__":
    main()
