import pandas as pd

def check_final_formats():
    """检查最终格式"""
    print("="*60)
    print("最终格式检查")
    print("="*60)
    
    try:
        df = pd.read_excel("作业登记表ABC栋202505~07.xlsx")
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 检查前10行关键列的格式
        print(f"\n前10行关键列格式检查:")
        print("行号 | A列(日期) | C列(时间) | K列(时间) | N列(时长)")
        print("-" * 60)
        
        for i in range(min(10, len(df))):
            a_val = str(df.iloc[i, 0])   # A列 - 日期
            c_val = str(df.iloc[i, 2])   # C列 - 起始时间
            k_val = str(df.iloc[i, 10])  # K列 - 实际开始时间
            n_val = str(df.iloc[i, 13])  # N列 - 实际作业时长
            
            print(f"{i+2:4d} | {a_val:9s} | {c_val:9s} | {k_val:9s} | {n_val:8s}")
        
        # 统计格式正确性
        print(f"\n格式统计:")
        
        # 日期格式统计
        date_correct = 0
        date_total = 0
        for val in df.iloc[:, 0]:
            if pd.notna(val) and str(val) != 'nan':
                date_total += 1
                val_str = str(val)
                if '/' in val_str and val_str.count('/') == 1:
                    date_correct += 1
        
        print(f"- A列日期(m/d): {date_correct}/{date_total} ({date_correct/date_total*100:.1f}%)")
        
        # 时间格式统计
        time_cols = [2, 3, 10, 11]  # C, D, K, L列
        time_correct = 0
        time_total = 0
        
        for col_idx in time_cols:
            for val in df.iloc[:, col_idx]:
                if pd.notna(val) and str(val) != 'nan':
                    time_total += 1
                    val_str = str(val)
                    if ':' in val_str and val_str.count(':') == 1:
                        time_correct += 1
        
        print(f"- 时间列(h:mm): {time_correct}/{time_total} ({time_correct/time_total*100:.1f}%)")
        
        # 时长格式统计
        duration_cols = [4, 12, 13]  # E, M, N列
        duration_correct = 0
        duration_total = 0
        
        for col_idx in duration_cols:
            for val in df.iloc[:, col_idx]:
                if pd.notna(val) and str(val) != 'nan':
                    duration_total += 1
                    val_str = str(val)
                    if ':' in val_str and val_str.count(':') == 1:
                        duration_correct += 1
        
        print(f"- 时长列(h:mm): {duration_correct}/{duration_total} ({duration_correct/duration_total*100:.1f}%)")
        
        # 总体评估
        overall_correct = date_correct + time_correct + duration_correct
        overall_total = date_total + time_total + duration_total
        overall_rate = overall_correct / overall_total * 100 if overall_total > 0 else 100
        
        print(f"\n总体格式正确率: {overall_rate:.1f}%")
        
        if overall_rate >= 99:
            print("✅ 格式检查通过！")
        else:
            print("⚠️  仍有少量格式问题")
        
        print(f"\n✅ 格式标准确认:")
        print(f"  - 日期格式: m/d (如 5/1, 12/25)")
        print(f"  - 时间格式: h:mm (如 9:30, 14:05)")
        print(f"  - 时长格式: h:mm (如 1:30, 2:00)")
        
    except Exception as e:
        print(f"检查时出错: {e}")

if __name__ == "__main__":
    check_final_formats()
