import pandas as pd
import numpy as np
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def convert_time_string_to_time_object(time_str):
    """将时间字符串转换为datetime.time对象"""
    if pd.isna(time_str) or str(time_str).strip() == '' or str(time_str) == 'nan':
        return None
    
    time_str = str(time_str).strip()
    
    try:
        # 处理 HH:MM:SS 格式
        if time_str.count(':') == 2:
            dt = datetime.strptime(time_str, '%H:%M:%S')
            return dt.time()
        # 处理 HH:MM 格式
        elif time_str.count(':') == 1:
            dt = datetime.strptime(time_str, '%H:%M')
            return dt.time()
        else:
            return None
    except:
        return None

def convert_duration_to_mixed_format(duration_str):
    """将时长转换为参考文件的混合格式"""
    if pd.isna(duration_str) or str(duration_str).strip() == '' or str(duration_str) == 'nan':
        return None
    
    duration_str = str(duration_str).strip()
    
    try:
        if ':' in duration_str:
            parts = duration_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 根据参考文件的模式，某些格式返回字符串，某些返回time对象
            # 观察参考文件：'1:00', datetime.time(1, 35), '0:50', '1:00', '1:00', '1:57', '1:00', '2:00', '1:40', '0:30'
            # 似乎整点时间用字符串，非整点用time对象
            
            if minutes == 0:
                return f"{hours}:00"  # 整点用字符串格式
            else:
                # 非整点用time对象格式
                return time(hours, minutes)
        else:
            return duration_str
    except:
        return duration_str

def precise_format_adjustment(file_path):
    """精确调整文件格式以匹配参考文件"""
    print(f"\n{'='*60}")
    print(f"精确格式调整: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取文件
        df = pd.read_excel(file_path)
        
        print(f"调整前文件信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 调整时间列格式 (K列和L列)
        time_columns = [10, 11]  # K列和L列的索引
        time_column_names = ['实际开始时间', '实际结束时间']
        
        for col_idx, col_name in zip(time_columns, time_column_names):
            print(f"\n调整 {col_name} (第{col_idx+1}列):")
            converted_count = 0
            
            for i in range(len(df)):
                original_value = df.iloc[i, col_idx]
                time_obj = convert_time_string_to_time_object(original_value)
                
                if time_obj is not None:
                    df.iloc[i, col_idx] = time_obj
                    converted_count += 1
            
            print(f"  转换了 {converted_count} 个时间值")
        
        # 调整作业时长格式 (N列)
        duration_col_idx = 13
        print(f"\n调整 实际作业时长（不含午休时长） (第{duration_col_idx+1}列):")
        converted_count = 0
        
        for i in range(len(df)):
            original_value = df.iloc[i, duration_col_idx]
            converted_value = convert_duration_to_mixed_format(original_value)
            
            if converted_value is not None:
                df.iloc[i, duration_col_idx] = converted_value
                converted_count += 1
        
        print(f"  转换了 {converted_count} 个时长值")
        
        # 显示调整后的样例
        print(f"\n调整后前10行关键列数据:")
        print("行号 | 开始时间 | 结束时间 | 作业时长")
        print("-" * 50)
        
        for i in range(min(10, len(df))):
            start_time = df.iloc[i, 10]
            end_time = df.iloc[i, 11]
            duration = df.iloc[i, 13]
            
            print(f"{i+1:3d}  | {start_time} | {end_time} | {duration}")
        
        return df
        
    except Exception as e:
        print(f"调整文件时出错: {e}")
        return None

def final_format_verification():
    """最终格式验证"""
    print(f"\n{'='*60}")
    print("最终格式验证")
    print(f"{'='*60}")
    
    files = [
        "作业登记表ABC栋202505.xlsx",  # 参考文件
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    file_data = {}
    
    for file_path in files:
        try:
            df = pd.read_excel(file_path)
            file_data[file_path] = df
            
            print(f"\n{file_path}:")
            print(f"  数据形状: {df.shape}")
            print(f"  列数: {len(df.columns)}")
            
            # 检查关键列的数据类型
            start_time_col = df.iloc[:, 10]  # K列
            end_time_col = df.iloc[:, 11]    # L列
            duration_col = df.iloc[:, 13]    # N列
            
            print(f"  开始时间列类型: {type(start_time_col.iloc[0])}")
            print(f"  结束时间列类型: {type(end_time_col.iloc[0])}")
            print(f"  作业时长列类型: {type(duration_col.iloc[0])}")
            
            # 显示前3个值的具体类型
            print(f"  开始时间前3个值: {[type(x).__name__ + ':' + str(x) for x in start_time_col.iloc[:3]]}")
            print(f"  结束时间前3个值: {[type(x).__name__ + ':' + str(x) for x in end_time_col.iloc[:3]]}")
            print(f"  作业时长前3个值: {[type(x).__name__ + ':' + str(x) for x in duration_col.iloc[:3]]}")
            
        except Exception as e:
            print(f"  读取 {file_path} 时出错: {e}")
    
    # 比较格式一致性
    if len(file_data) >= 2:
        ref_file = "作业登记表ABC栋202505.xlsx"
        if ref_file in file_data:
            ref_df = file_data[ref_file]
            
            print(f"\n格式一致性检查:")
            for file_path, df in file_data.items():
                if file_path != ref_file:
                    print(f"\n{file_path} vs 参考文件:")
                    
                    # 检查列名
                    columns_match = list(df.columns) == list(ref_df.columns)
                    print(f"  列名一致: {'✅' if columns_match else '❌'}")
                    
                    # 检查数据形状
                    print(f"  数据形状: {df.shape} (参考: {ref_df.shape})")

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 精确调整格式
        adjusted_df = precise_format_adjustment(file_path)
        
        if adjusted_df is not None:
            # 保存调整后的文件
            adjusted_df.to_excel(file_path, index=False)
            print(f"✅ 已更新文件: {file_path}")
        else:
            print(f"❌ 文件调整失败: {file_path}")
    
    # 最终验证
    final_format_verification()

if __name__ == "__main__":
    main()
