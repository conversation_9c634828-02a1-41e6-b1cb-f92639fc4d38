import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def parse_time_string(time_str):
    """解析时间字符串，返回datetime对象"""
    if pd.isna(time_str) or time_str == '' or str(time_str).strip() == '':
        return None
    
    time_str = str(time_str).strip()
    
    # 处理各种时间格式
    try:
        # 格式1: HH:MM:SS
        if ':' in time_str and len(time_str.split(':')) == 3:
            return datetime.strptime(time_str, '%H:%M:%S')
        # 格式2: HH:MM
        elif ':' in time_str and len(time_str.split(':')) == 2:
            return datetime.strptime(time_str, '%H:%M')
        # 格式3: 纯数字（如1430表示14:30）
        elif time_str.isdigit() and len(time_str) == 4:
            hour = int(time_str[:2])
            minute = int(time_str[2:])
            return datetime.strptime(f'{hour:02d}:{minute:02d}', '%H:%M')
        else:
            print(f"无法解析时间格式: {time_str}")
            return None
    except Exception as e:
        print(f"解析时间出错 '{time_str}': {e}")
        return None

def calculate_work_duration(start_time, end_time):
    """
    计算实际作业时长（不含午休时长）
    午休时间：11:30-12:30
    """
    if start_time is None or end_time is None:
        return None
    
    # 午休时间定义
    lunch_start = datetime.strptime('11:30', '%H:%M')
    lunch_end = datetime.strptime('12:30', '%H:%M')
    
    # 计算总时长
    total_duration = end_time - start_time
    
    # 检查是否跨越午休时间
    lunch_overlap = timedelta(0)
    
    # 如果开始时间在午休前，结束时间在午休后
    if start_time <= lunch_start and end_time >= lunch_end:
        lunch_overlap = timedelta(hours=1)  # 完整午休时间
    # 如果开始时间在午休期间
    elif lunch_start < start_time < lunch_end and end_time >= lunch_end:
        lunch_overlap = lunch_end - start_time
    # 如果结束时间在午休期间
    elif start_time <= lunch_start and lunch_start < end_time < lunch_end:
        lunch_overlap = end_time - lunch_start
    # 如果开始和结束都在午休期间
    elif lunch_start <= start_time < lunch_end and lunch_start < end_time <= lunch_end:
        lunch_overlap = end_time - start_time
    
    # 实际工作时长 = 总时长 - 午休重叠时长
    work_duration = total_duration - lunch_overlap
    
    # 确保不为负数
    if work_duration.total_seconds() < 0:
        work_duration = timedelta(0)
    
    return work_duration

def format_duration(duration):
    """将timedelta格式化为时:分的字符串"""
    if duration is None:
        return ""
    
    total_seconds = int(duration.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    
    return f"{hours}:{minutes:02d}"

def process_excel_file(file_path):
    """处理单个Excel文件"""
    print(f"\n{'='*60}")
    print(f"处理文件: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n列名信息:")
        for i, col in enumerate(df.columns):
            print(f"第{i+1}列 ({chr(65+i)}列): {col}")
        
        # 找到关键列的索引
        start_time_col = None
        end_time_col = None
        duration_col = None
        
        for i, col in enumerate(df.columns):
            if '实际开始时间' in str(col):
                start_time_col = i
            elif '实际结束时间' in str(col):
                end_time_col = i
            elif '实际作业时长（不含午休时长）' in str(col):
                duration_col = i
        
        if start_time_col is None or end_time_col is None or duration_col is None:
            print("错误：找不到必要的列")
            return None
        
        print(f"\n关键列位置:")
        print(f"- 实际开始时间: 第{start_time_col+1}列 ({chr(65+start_time_col)}列)")
        print(f"- 实际结束时间: 第{end_time_col+1}列 ({chr(65+end_time_col)}列)")
        print(f"- 实际作业时长（不含午休时长）: 第{duration_col+1}列 ({chr(65+duration_col)}列)")
        
        # 处理数据
        processed_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            start_time_str = row.iloc[start_time_col]
            end_time_str = row.iloc[end_time_col]
            
            # 解析时间
            start_time = parse_time_string(start_time_str)
            end_time = parse_time_string(end_time_str)
            
            if start_time is not None and end_time is not None:
                # 计算工作时长
                work_duration = calculate_work_duration(start_time, end_time)
                duration_str = format_duration(work_duration)
                
                # 更新N列
                df.iloc[index, duration_col] = duration_str
                processed_count += 1
                
                # 显示前几个处理结果
                if processed_count <= 10:
                    print(f"行{index+1}: {start_time_str} → {end_time_str} = {duration_str}")
            else:
                error_count += 1
                if error_count <= 5:  # 只显示前5个错误
                    print(f"行{index+1}: 时间解析失败 - 开始:{start_time_str}, 结束:{end_time_str}")
        
        print(f"\n处理结果:")
        print(f"- 成功处理: {processed_count} 行")
        print(f"- 处理失败: {error_count} 行")
        
        return df
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def main():
    """主函数"""
    files_to_process = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in files_to_process:
        df = process_excel_file(file_path)
        
        if df is not None:
            # 保存处理后的文件
            output_path = file_path.replace('.xlsx', '_processed.xlsx')
            df.to_excel(output_path, index=False)
            print(f"✅ 处理完成，已保存到: {output_path}")
        else:
            print(f"❌ 文件处理失败: {file_path}")

if __name__ == "__main__":
    main()
