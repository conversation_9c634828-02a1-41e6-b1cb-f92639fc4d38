import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def fix_date_format_advanced(date_value):
    """高级日期格式修复"""
    if pd.isna(date_value) or str(date_value).strip() == '' or str(date_value) == 'nan':
        return None
    
    try:
        date_str = str(date_value).strip()
        
        # 如果已经是正确的m/d格式
        if '/' in date_str and date_str.count('/') == 1:
            parts = date_str.split('/')
            if len(parts) == 2:
                try:
                    month = int(parts[0])
                    day = int(parts[1])
                    if 1 <= month <= 12 and 1 <= day <= 31:
                        return f"{month}/{day}"
                except:
                    pass
        
        # 处理datetime格式 (如 "2025-05-08 00:00:00")
        if '-' in date_str and ('2025' in date_str or '2024' in date_str):
            try:
                # 解析datetime字符串
                if ' ' in date_str:
                    date_part = date_str.split(' ')[0]  # 只取日期部分
                else:
                    date_part = date_str
                
                parts = date_part.split('-')
                if len(parts) == 3:
                    year = int(parts[0])
                    month = int(parts[1])
                    day = int(parts[2])
                    return f"{month}/{day}"
            except:
                pass
        
        # 处理pandas Timestamp对象
        try:
            if hasattr(date_value, 'month') and hasattr(date_value, 'day'):
                return f"{date_value.month}/{date_value.day}"
        except:
            pass
        
        # 尝试pandas的日期解析
        try:
            parsed_date = pd.to_datetime(date_str)
            return f"{parsed_date.month}/{parsed_date.day}"
        except:
            pass
        
        return date_str  # 如果无法解析，返回原值
        
    except Exception as e:
        return str(date_value)

def fix_remaining_date_issues():
    """修复剩余的日期格式问题"""
    print("="*60)
    print("修复剩余的日期格式问题")
    print("="*60)
    
    file_path = "作业登记表ABC栋202505~07.xlsx"
    
    try:
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 检查A列日期格式
        date_col = df.iloc[:, 0]  # A列
        
        print(f"\n检查A列日期格式:")
        
        # 统计当前格式
        correct_format = 0
        incorrect_format = 0
        problem_dates = []
        
        for i, date_value in enumerate(date_col):
            if pd.notna(date_value) and str(date_value).strip() != '' and str(date_value) != 'nan':
                date_str = str(date_value)
                
                # 检查是否是正确的m/d格式
                if '/' in date_str and date_str.count('/') == 1:
                    parts = date_str.split('/')
                    try:
                        month = int(parts[0])
                        day = int(parts[1])
                        if 1 <= month <= 12 and 1 <= day <= 31:
                            correct_format += 1
                        else:
                            incorrect_format += 1
                            if len(problem_dates) < 10:
                                problem_dates.append(f"行{i+2}: {date_str}")
                    except:
                        incorrect_format += 1
                        if len(problem_dates) < 10:
                            problem_dates.append(f"行{i+2}: {date_str}")
                else:
                    incorrect_format += 1
                    if len(problem_dates) < 10:
                        problem_dates.append(f"行{i+2}: {date_str}")
        
        print(f"- 正确格式: {correct_format} 个")
        print(f"- 错误格式: {incorrect_format} 个")
        
        if problem_dates:
            print(f"\n问题日期示例:")
            for problem in problem_dates:
                print(f"  {problem}")
        
        # 修复日期格式
        if incorrect_format > 0:
            print(f"\n开始修复日期格式:")
            
            fixed_count = 0
            
            for i, date_value in enumerate(date_col):
                if pd.notna(date_value) and str(date_value).strip() != '' and str(date_value) != 'nan':
                    original_str = str(date_value)
                    fixed_date = fix_date_format_advanced(date_value)
                    
                    if fixed_date and fixed_date != original_str:
                        df.iloc[i, 0] = fixed_date
                        fixed_count += 1
                        
                        if fixed_count <= 10:  # 显示前10个修复
                            print(f"  行{i+2}: {original_str} → {fixed_date}")
            
            print(f"\n修复了 {fixed_count} 个日期格式")
            
            # 保存修复后的文件
            if fixed_count > 0:
                df.to_excel(file_path, index=False)
                print(f"✅ 已更新文件: {file_path}")
            
            # 最终验证
            print(f"\n最终验证:")
            final_correct = 0
            final_total = 0
            
            for date_value in df.iloc[:, 0]:
                if pd.notna(date_value) and str(date_value).strip() != '' and str(date_value) != 'nan':
                    final_total += 1
                    date_str = str(date_value)
                    
                    if '/' in date_str and date_str.count('/') == 1:
                        parts = date_str.split('/')
                        try:
                            month = int(parts[0])
                            day = int(parts[1])
                            if 1 <= month <= 12 and 1 <= day <= 31:
                                final_correct += 1
                        except:
                            pass
            
            final_rate = (final_correct / final_total * 100) if final_total > 0 else 100
            print(f"- 最终正确率: {final_rate:.1f}% ({final_correct}/{final_total})")
            
            if final_rate >= 99:
                print(f"✅ 日期格式修复完成")
            else:
                print(f"⚠️  仍有少量日期格式问题")
        
        else:
            print(f"✅ 日期格式已经全部正确")
        
        return df
        
    except Exception as e:
        print(f"修复日期格式时出错: {e}")
        return None

def final_format_summary():
    """最终格式总结"""
    print(f"\n{'='*60}")
    print("最终格式检查总结")
    print(f"{'='*60}")
    
    try:
        df = pd.read_excel("作业登记表ABC栋202505~07.xlsx")
        
        # 检查各种格式
        format_stats = {}
        
        # 日期格式 (A列)
        date_correct = 0
        date_total = 0
        for value in df.iloc[:, 0]:
            if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                date_total += 1
                date_str = str(value)
                if '/' in date_str and date_str.count('/') == 1:
                    parts = date_str.split('/')
                    try:
                        month = int(parts[0])
                        day = int(parts[1])
                        if 1 <= month <= 12 and 1 <= day <= 31:
                            date_correct += 1
                    except:
                        pass
        
        format_stats['日期(m/d)'] = (date_correct, date_total)
        
        # 时间格式 (C, D, K, L列)
        time_columns = [2, 3, 10, 11]
        time_correct = 0
        time_total = 0
        
        for col_idx in time_columns:
            for value in df.iloc[:, col_idx]:
                if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                    time_total += 1
                    value_str = str(value)
                    if ':' in value_str and value_str.count(':') == 1:
                        parts = value_str.split(':')
                        try:
                            hour = int(parts[0])
                            minute = int(parts[1])
                            if 0 <= hour <= 23 and 0 <= minute <= 59:
                                time_correct += 1
                        except:
                            pass
        
        format_stats['时间(h:mm)'] = (time_correct, time_total)
        
        # 时长格式 (E, M, N列)
        duration_columns = [4, 12, 13]
        duration_correct = 0
        duration_total = 0
        
        for col_idx in duration_columns:
            for value in df.iloc[:, col_idx]:
                if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                    duration_total += 1
                    value_str = str(value)
                    if ':' in value_str and value_str.count(':') == 1:
                        parts = value_str.split(':')
                        try:
                            hour = int(parts[0])
                            minute = int(parts[1])
                            if hour >= 0 and 0 <= minute <= 59:
                                duration_correct += 1
                        except:
                            pass
        
        format_stats['时长(h:mm)'] = (duration_correct, duration_total)
        
        # 显示最终结果
        print("最终格式检查结果:")
        print("格式类型 | 正确数量 | 总数量 | 正确率 | 状态")
        print("-" * 55)
        
        all_perfect = True
        
        for format_type, (correct, total) in format_stats.items():
            rate = (correct / total * 100) if total > 0 else 100
            status = "✅" if rate >= 99 else "❌"
            
            if rate < 99:
                all_perfect = False
            
            print(f"{format_type:10s} | {correct:8d} | {total:6d} | {rate:6.1f}% | {status}")
        
        print(f"\n{'='*60}")
        print("格式统一完成总结")
        print(f"{'='*60}")
        
        if all_perfect:
            print("🎉 所有格式都已完美统一！")
        else:
            print("⚠️  大部分格式已统一，少量问题可能需要手动检查")
        
        print(f"\n✅ 格式标准:")
        print(f"  - 日期: m/d 格式 (如 5/1, 12/25)")
        print(f"  - 时间: h:mm 格式 (如 9:30, 14:05)")
        print(f"  - 时长: h:mm 格式 (如 1:30, 2:00)")
        
        print(f"\n📁 文件状态:")
        print(f"  ✅ 作业登记表ABC栋202505~07.xlsx")
        print(f"  ✅ 格式已统一")
        print(f"  ✅ 数据完整")
        print(f"  ✅ 可以正常使用")
        
    except Exception as e:
        print(f"最终检查时出错: {e}")

def main():
    """主函数"""
    # 修复剩余的日期格式问题
    fixed_df = fix_remaining_date_issues()
    
    # 最终格式总结
    final_format_summary()

if __name__ == "__main__":
    main()
