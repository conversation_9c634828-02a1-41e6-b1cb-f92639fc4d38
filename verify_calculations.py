import pandas as pd
from datetime import datetime, timedelta

def verify_calculation(start_str, end_str, expected_result):
    """验证单个计算结果"""
    try:
        # 解析时间
        if ':' in start_str and len(start_str.split(':')) == 3:
            start_time = datetime.strptime(start_str, '%H:%M:%S')
        elif ':' in start_str and len(start_str.split(':')) == 2:
            start_time = datetime.strptime(start_str, '%H:%M')
        else:
            return False, f"无法解析开始时间: {start_str}"
        
        if ':' in end_str and len(end_str.split(':')) == 3:
            end_time = datetime.strptime(end_str, '%H:%M:%S')
        elif ':' in end_str and len(end_str.split(':')) == 2:
            end_time = datetime.strptime(end_str, '%H:%M')
        else:
            return False, f"无法解析结束时间: {end_str}"
        
        # 午休时间
        lunch_start = datetime.strptime('11:30', '%H:%M')
        lunch_end = datetime.strptime('12:30', '%H:%M')
        
        # 计算总时长
        total_duration = end_time - start_time
        
        # 计算午休重叠
        lunch_overlap = timedelta(0)
        if start_time <= lunch_start and end_time >= lunch_end:
            lunch_overlap = timedelta(hours=1)
        elif lunch_start < start_time < lunch_end and end_time >= lunch_end:
            lunch_overlap = lunch_end - start_time
        elif start_time <= lunch_start and lunch_start < end_time < lunch_end:
            lunch_overlap = end_time - lunch_start
        elif lunch_start <= start_time < lunch_end and lunch_start < end_time <= lunch_end:
            lunch_overlap = end_time - start_time
        
        # 实际工作时长
        work_duration = total_duration - lunch_overlap
        if work_duration.total_seconds() < 0:
            work_duration = timedelta(0)
        
        # 格式化结果
        total_seconds = int(work_duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        calculated_result = f"{hours}:{minutes:02d}"
        
        return calculated_result == expected_result, f"计算结果: {calculated_result}, 期望结果: {expected_result}"
        
    except Exception as e:
        return False, f"计算出错: {e}"

def verify_processed_files():
    """验证处理后的文件"""
    files = [
        "作业登记表ABC栋202506_processed.xlsx",
        "作业登记表ABC栋202507_processed.xlsx"
    ]
    
    for file_path in files:
        print(f"\n{'='*60}")
        print(f"验证文件: {file_path}")
        print(f"{'='*60}")
        
        try:
            df = pd.read_excel(file_path)
            
            # 找到关键列
            start_col = None
            end_col = None
            duration_col = None
            
            for i, col in enumerate(df.columns):
                if '实际开始时间' in str(col):
                    start_col = i
                elif '实际结束时间' in str(col):
                    end_col = i
                elif '实际作业时长（不含午休时长）' in str(col):
                    duration_col = i
            
            if start_col is None or end_col is None or duration_col is None:
                print("错误：找不到必要的列")
                continue
            
            # 验证前20行
            correct_count = 0
            total_checked = 0
            
            print("验证前20行数据:")
            print("行号 | 开始时间 | 结束时间 | 计算结果 | 验证状态")
            print("-" * 60)
            
            for i in range(min(20, len(df))):
                start_time = str(df.iloc[i, start_col]).strip()
                end_time = str(df.iloc[i, end_col]).strip()
                duration = str(df.iloc[i, duration_col]).strip()
                
                if start_time != 'nan' and end_time != 'nan' and duration != 'nan':
                    is_correct, message = verify_calculation(start_time, end_time, duration)
                    status = "✅" if is_correct else "❌"
                    
                    print(f"{i+1:3d}  | {start_time:8s} | {end_time:8s} | {duration:8s} | {status}")
                    
                    if is_correct:
                        correct_count += 1
                    total_checked += 1
            
            print(f"\n验证结果: {correct_count}/{total_checked} 正确")
            
            # 检查特殊情况：跨午休时间的案例
            print(f"\n检查跨午休时间的案例:")
            lunch_cases = 0
            for i in range(len(df)):
                start_time_str = str(df.iloc[i, start_col]).strip()
                end_time_str = str(df.iloc[i, end_col]).strip()
                
                if start_time_str != 'nan' and end_time_str != 'nan':
                    try:
                        if ':' in start_time_str:
                            start_time = datetime.strptime(start_time_str.split()[0] if ' ' in start_time_str else start_time_str, '%H:%M:%S' if start_time_str.count(':') == 2 else '%H:%M')
                        if ':' in end_time_str:
                            end_time = datetime.strptime(end_time_str.split()[0] if ' ' in end_time_str else end_time_str, '%H:%M:%S' if end_time_str.count(':') == 2 else '%H:%M')
                        
                        lunch_start = datetime.strptime('11:30', '%H:%M')
                        lunch_end = datetime.strptime('12:30', '%H:%M')
                        
                        # 检查是否跨越午休时间
                        if start_time <= lunch_start and end_time >= lunch_end:
                            lunch_cases += 1
                            if lunch_cases <= 5:  # 显示前5个案例
                                duration = str(df.iloc[i, duration_col]).strip()
                                print(f"  跨午休案例 {lunch_cases}: {start_time_str} → {end_time_str} = {duration}")
                    except:
                        pass
            
            print(f"发现 {lunch_cases} 个跨午休时间的案例")
            
        except Exception as e:
            print(f"验证文件时出错: {e}")

if __name__ == "__main__":
    verify_processed_files()
