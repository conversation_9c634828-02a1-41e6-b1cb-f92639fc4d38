import pandas as pd
import numpy as np
from datetime import datetime, time
from openpyxl import load_workbook
import warnings
warnings.filterwarnings('ignore')

def check_current_formats(file_path):
    """检查当前文件的格式"""
    print(f"{'='*60}")
    print(f"检查文件格式: {file_path}")
    print(f"{'='*60}")
    
    try:
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n列结构:")
        for i, col in enumerate(df.columns):
            print(f"第{i+1}列 ({chr(65+i)}列): {col}")
        
        # 检查关键列的格式
        key_columns = [
            (0, "日期", "A"),
            (2, "起始时间", "C"),
            (3, "截止时间", "D"),
            (4, "作业时长", "E"),
            (10, "实际开始时间", "K"),
            (11, "实际结束时间", "L"),
            (12, "实际作业时长", "M"),
            (13, "实际作业时长（不含午休时长）", "N")
        ]
        
        print(f"\n格式检查结果:")
        print("列 | 列名 | 样例值 | 格式问题")
        print("-" * 60)
        
        format_issues = []
        
        for col_idx, col_name, col_letter in key_columns:
            if col_idx < len(df.columns):
                # 获取前5个非空值作为样例
                sample_values = []
                format_problems = []
                
                for i in range(len(df)):
                    value = df.iloc[i, col_idx]
                    if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                        if len(sample_values) < 3:
                            sample_values.append(str(value))
                        
                        # 检查格式问题
                        value_str = str(value)
                        
                        if col_idx == 0:  # 日期列
                            # 检查是否是m/d格式
                            if '/' not in value_str:
                                format_problems.append(f"非m/d格式: {value_str}")
                            elif value_str.count('/') != 1:
                                format_problems.append(f"格式错误: {value_str}")
                        
                        elif col_idx in [2, 3, 10, 11]:  # 时间列
                            # 检查是否是h:mm格式
                            if ':' not in value_str:
                                format_problems.append(f"非时间格式: {value_str}")
                            elif value_str.count(':') > 1:  # 有秒数
                                format_problems.append(f"包含秒数: {value_str}")
                        
                        elif col_idx in [4, 12, 13]:  # 时长列
                            # 检查是否是h:mm格式
                            if ':' not in value_str:
                                format_problems.append(f"非时长格式: {value_str}")
                            elif value_str.count(':') > 1:  # 有秒数
                                format_problems.append(f"包含秒数: {value_str}")
                
                # 统计问题
                unique_problems = list(set(format_problems))
                problem_count = len(format_problems)
                
                sample_str = ", ".join(sample_values)
                problem_str = f"{problem_count}个问题" if problem_count > 0 else "✅ 正常"
                
                print(f"{col_letter:2s} | {col_name[:12]:12s} | {sample_str[:20]:20s} | {problem_str}")
                
                if problem_count > 0:
                    format_issues.append({
                        'column': col_letter,
                        'name': col_name,
                        'problems': unique_problems[:3],  # 只显示前3个问题类型
                        'count': problem_count
                    })
        
        return format_issues
        
    except Exception as e:
        print(f"检查格式时出错: {e}")
        return []

def fix_date_format(date_value):
    """修复日期格式为m/d"""
    if pd.isna(date_value) or str(date_value).strip() == '' or str(date_value) == 'nan':
        return None
    
    try:
        date_str = str(date_value).strip()
        
        # 如果已经是m/d格式，检查是否正确
        if '/' in date_str:
            parts = date_str.split('/')
            if len(parts) == 2:
                try:
                    month = int(parts[0])
                    day = int(parts[1])
                    if 1 <= month <= 12 and 1 <= day <= 31:
                        return f"{month}/{day}"
                except:
                    pass
        
        # 尝试解析其他日期格式
        try:
            # 尝试解析为日期对象
            if '-' in date_str:
                # 处理 YYYY-MM-DD 或 MM-DD 格式
                parts = date_str.split('-')
                if len(parts) == 3:  # YYYY-MM-DD
                    month = int(parts[1])
                    day = int(parts[2])
                elif len(parts) == 2:  # MM-DD
                    month = int(parts[0])
                    day = int(parts[1])
                else:
                    return date_str
                return f"{month}/{day}"
            
            # 尝试pandas的日期解析
            parsed_date = pd.to_datetime(date_str)
            return f"{parsed_date.month}/{parsed_date.day}"
            
        except:
            pass
        
        return date_str  # 如果无法解析，返回原值
        
    except Exception as e:
        return str(date_value)

def fix_time_format(time_value):
    """修复时间格式为h:mm"""
    if pd.isna(time_value) or str(time_value).strip() == '' or str(time_value) == 'nan':
        return None
    
    try:
        time_str = str(time_value).strip()
        
        # 如果是time对象
        if isinstance(time_value, time):
            return f"{time_value.hour}:{time_value.minute:02d}"
        
        # 如果是字符串格式
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) >= 2:
                try:
                    hour = int(parts[0])
                    minute = int(parts[1])
                    # 忽略秒数部分
                    return f"{hour}:{minute:02d}"
                except:
                    pass
        
        # 尝试解析其他格式
        try:
            # 如果是纯数字（如1430表示14:30）
            if time_str.isdigit() and len(time_str) == 4:
                hour = int(time_str[:2])
                minute = int(time_str[2:])
                return f"{hour}:{minute:02d}"
        except:
            pass
        
        return time_str  # 如果无法解析，返回原值
        
    except Exception as e:
        return str(time_value)

def fix_all_formats(file_path):
    """修复所有格式问题"""
    print(f"\n{'='*60}")
    print(f"修复格式问题: {file_path}")
    print(f"{'='*60}")
    
    try:
        df = pd.read_excel(file_path)
        
        # 定义需要处理的列
        date_columns = [0]  # A列 - 日期
        time_columns = [2, 3, 10, 11]  # C, D, K, L列 - 时间
        duration_columns = [4, 12, 13]  # E, M, N列 - 时长
        
        fixed_counts = {
            'dates': 0,
            'times': 0,
            'durations': 0
        }
        
        # 修复日期格式
        print(f"修复日期格式 (A列):")
        for col_idx in date_columns:
            if col_idx < len(df.columns):
                for i in range(len(df)):
                    original_value = df.iloc[i, col_idx]
                    fixed_value = fix_date_format(original_value)
                    
                    if fixed_value is not None and str(fixed_value) != str(original_value):
                        df.iloc[i, col_idx] = fixed_value
                        fixed_counts['dates'] += 1
                        
                        if fixed_counts['dates'] <= 5:  # 显示前5个修复
                            print(f"  行{i+2}: {original_value} → {fixed_value}")
        
        print(f"  修复了 {fixed_counts['dates']} 个日期格式")
        
        # 修复时间格式
        print(f"\n修复时间格式 (C, D, K, L列):")
        for col_idx in time_columns:
            if col_idx < len(df.columns):
                col_name = df.columns[col_idx]
                col_fixed = 0
                
                for i in range(len(df)):
                    original_value = df.iloc[i, col_idx]
                    fixed_value = fix_time_format(original_value)
                    
                    if fixed_value is not None and str(fixed_value) != str(original_value):
                        df.iloc[i, col_idx] = fixed_value
                        col_fixed += 1
                        fixed_counts['times'] += 1
                        
                        if col_fixed <= 3:  # 每列显示前3个修复
                            print(f"  {chr(65+col_idx)}列行{i+2}: {original_value} → {fixed_value}")
                
                if col_fixed > 3:
                    print(f"  {chr(65+col_idx)}列: 共修复 {col_fixed} 个")
        
        print(f"  总计修复了 {fixed_counts['times']} 个时间格式")
        
        # 修复时长格式
        print(f"\n修复时长格式 (E, M, N列):")
        for col_idx in duration_columns:
            if col_idx < len(df.columns):
                col_name = df.columns[col_idx]
                col_fixed = 0
                
                for i in range(len(df)):
                    original_value = df.iloc[i, col_idx]
                    fixed_value = fix_time_format(original_value)  # 时长也用相同的格式
                    
                    if fixed_value is not None and str(fixed_value) != str(original_value):
                        df.iloc[i, col_idx] = fixed_value
                        col_fixed += 1
                        fixed_counts['durations'] += 1
                        
                        if col_fixed <= 3:  # 每列显示前3个修复
                            print(f"  {chr(65+col_idx)}列行{i+2}: {original_value} → {fixed_value}")
                
                if col_fixed > 3:
                    print(f"  {chr(65+col_idx)}列: 共修复 {col_fixed} 个")
        
        print(f"  总计修复了 {fixed_counts['durations']} 个时长格式")
        
        return df, fixed_counts
        
    except Exception as e:
        print(f"修复格式时出错: {e}")
        return None, {}

def verify_final_formats(df):
    """验证最终格式"""
    print(f"\n{'='*60}")
    print("最终格式验证")
    print(f"{'='*60}")
    
    try:
        # 验证关键列
        verification_results = {}
        
        # 验证日期格式 (A列)
        date_correct = 0
        date_total = 0
        
        for value in df.iloc[:, 0]:
            if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                date_total += 1
                value_str = str(value)
                if '/' in value_str and value_str.count('/') == 1:
                    parts = value_str.split('/')
                    try:
                        month = int(parts[0])
                        day = int(parts[1])
                        if 1 <= month <= 12 and 1 <= day <= 31:
                            date_correct += 1
                    except:
                        pass
        
        verification_results['dates'] = (date_correct, date_total)
        
        # 验证时间格式 (C, D, K, L列)
        time_columns = [2, 3, 10, 11]
        time_correct = 0
        time_total = 0
        
        for col_idx in time_columns:
            if col_idx < len(df.columns):
                for value in df.iloc[:, col_idx]:
                    if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                        time_total += 1
                        value_str = str(value)
                        if ':' in value_str and value_str.count(':') == 1:
                            parts = value_str.split(':')
                            try:
                                hour = int(parts[0])
                                minute = int(parts[1])
                                if 0 <= hour <= 23 and 0 <= minute <= 59:
                                    time_correct += 1
                            except:
                                pass
        
        verification_results['times'] = (time_correct, time_total)
        
        # 验证时长格式 (E, M, N列)
        duration_columns = [4, 12, 13]
        duration_correct = 0
        duration_total = 0
        
        for col_idx in duration_columns:
            if col_idx < len(df.columns):
                for value in df.iloc[:, col_idx]:
                    if pd.notna(value) and str(value).strip() != '' and str(value) != 'nan':
                        duration_total += 1
                        value_str = str(value)
                        if ':' in value_str and value_str.count(':') == 1:
                            parts = value_str.split(':')
                            try:
                                hour = int(parts[0])
                                minute = int(parts[1])
                                if hour >= 0 and 0 <= minute <= 59:
                                    duration_correct += 1
                            except:
                                pass
        
        verification_results['durations'] = (duration_correct, duration_total)
        
        # 显示验证结果
        print("格式验证结果:")
        print("类型 | 正确数量 | 总数量 | 正确率 | 状态")
        print("-" * 50)
        
        for format_type, (correct, total) in verification_results.items():
            rate = (correct / total * 100) if total > 0 else 100
            status = "✅" if rate >= 95 else "❌"
            
            type_name = {
                'dates': '日期(m/d)',
                'times': '时间(h:mm)',
                'durations': '时长(h:mm)'
            }[format_type]
            
            print(f"{type_name:10s} | {correct:8d} | {total:6d} | {rate:6.1f}% | {status}")
        
        return verification_results
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return {}

def main():
    """主函数"""
    file_path = "作业登记表ABC栋202505~07.xlsx"
    
    print("="*80)
    print("数据表格式检查和修复")
    print("="*80)
    
    # 1. 检查当前格式
    format_issues = check_current_formats(file_path)
    
    if format_issues:
        print(f"\n发现格式问题:")
        for issue in format_issues:
            print(f"- {issue['column']}列 ({issue['name']}): {issue['count']}个问题")
            for problem in issue['problems']:
                print(f"  * {problem}")
    
    # 2. 修复格式问题
    fixed_df, fixed_counts = fix_all_formats(file_path)
    
    if fixed_df is not None:
        # 3. 验证最终格式
        verification_results = verify_final_formats(fixed_df)
        
        # 4. 保存修复后的文件
        if any(count > 0 for count in fixed_counts.values()):
            output_path = file_path.replace('.xlsx', '_format_fixed.xlsx')
            fixed_df.to_excel(output_path, index=False)
            print(f"\n✅ 格式修复完成，已保存到: {output_path}")
            
            # 覆盖原文件
            fixed_df.to_excel(file_path, index=False)
            print(f"✅ 已更新原文件: {file_path}")
        else:
            print(f"\n✅ 格式已经正确，无需修复")
        
        # 5. 总结
        print(f"\n{'='*60}")
        print("格式修复总结")
        print(f"{'='*60}")
        
        print(f"✅ 格式标准:")
        print(f"  - 日期格式: m/d (如 5/1, 12/25)")
        print(f"  - 时间格式: h:mm (如 9:30, 14:05)")
        print(f"  - 时长格式: h:mm (如 1:30, 2:00)")
        
        print(f"\n📊 修复统计:")
        print(f"  - 日期修复: {fixed_counts.get('dates', 0)} 个")
        print(f"  - 时间修复: {fixed_counts.get('times', 0)} 个")
        print(f"  - 时长修复: {fixed_counts.get('durations', 0)} 个")
        
        print(f"\n📁 文件状态:")
        print(f"  ✅ 格式统一完成")
        print(f"  ✅ 数据完整保持")
        print(f"  ✅ 可以正常使用")
    
    else:
        print(f"\n❌ 格式修复失败")

if __name__ == "__main__":
    main()
