import pandas as pd

# 读取处理后的文件
df = pd.read_excel('作业登记表ABC栋202505~07_processed.xlsx')

print("处理后文件验证:")
print(f"数据形状: {df.shape}")
print("\n前10行关键数据:")
print("行号 | K列(开始) | L列(结束) | N列(时长)")
print("-" * 45)

for i in range(10):
    k_val = df.iloc[i, 10]  # K列
    l_val = df.iloc[i, 11]  # L列  
    n_val = df.iloc[i, 13]  # N列
    print(f"{i+2:4d} | {str(k_val):9s} | {str(l_val):9s} | {str(n_val):8s}")

print(f"\n✅ 处理完成！文件已成功更新。")
