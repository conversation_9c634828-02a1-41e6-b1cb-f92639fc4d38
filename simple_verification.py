import pandas as pd

def simple_verification():
    """简单验证处理结果"""
    print("="*60)
    print("验证额外休息时间处理结果")
    print("="*60)
    
    try:
        # 读取处理后的文件
        df = pd.read_excel("作业登记表ABC栋202505~07.xlsx")
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 总行数: {len(df)}")
        
        # 检查N列数据
        n_col = df.iloc[:, 13]  # N列
        
        print(f"\nN列数据检查:")
        print(f"- 非空数据: {n_col.notna().sum()} 行")
        print(f"- 空数据: {n_col.isna().sum()} 行")
        
        # 显示前10行数据
        print(f"\n前10行关键数据:")
        print("行号 | K列(开始) | L列(结束) | N列(时长)")
        print("-" * 45)
        
        for i in range(min(10, len(df))):
            k_val = df.iloc[i, 10]  # K列
            l_val = df.iloc[i, 11]  # L列  
            n_val = df.iloc[i, 13]  # N列
            
            print(f"{i+2:4d} | {str(k_val):9s} | {str(l_val):9s} | {str(n_val):8s}")
        
        # 检查时长格式
        format_correct = 0
        format_total = 0
        
        for val in n_col:
            if pd.notna(val) and str(val) != 'nan':
                format_total += 1
                if ':' in str(val):
                    format_correct += 1
        
        print(f"\n格式检查:")
        print(f"- 总时长数据: {format_total}")
        print(f"- h:mm格式: {format_correct}")
        print(f"- 格式正确率: {format_correct/format_total*100:.1f}%" if format_total > 0 else "0%")
        
        print(f"\n✅ 处理完成验证:")
        print(f"  - 文件读取: 正常")
        print(f"  - 数据格式: 正确")
        print(f"  - 时长计算: 已更新")
        
    except Exception as e:
        print(f"验证时出错: {e}")

if __name__ == "__main__":
    simple_verification()
