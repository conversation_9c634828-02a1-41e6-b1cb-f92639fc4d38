import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import Pat<PERSON><PERSON>ill, Font
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def parse_time_duration(time_str):
    """解析时间时长字符串，返回总分钟数"""
    if pd.isna(time_str) or str(time_str).strip() == '' or str(time_str) == 'nan':
        return 0
    
    try:
        time_str = str(time_str).strip()
        
        # 如果是time对象
        if isinstance(time_str, time):
            return time_str.hour * 60 + time_str.minute
        
        # 如果是字符串格式 h:mm
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) >= 2:
                hours = int(parts[0])
                minutes = int(parts[1])
                return hours * 60 + minutes
        
        # 如果是纯数字，假设是分钟
        if time_str.replace('.', '').isdigit():
            return int(float(time_str))
        
        return 0
        
    except Exception as e:
        print(f"解析时间时长时出错 '{time_str}': {e}")
        return 0

def improved_highlight_overtime(file_path):
    """改进的超时标记功能"""
    print(f"\n{'='*60}")
    print(f"改进处理文件: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取数据
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        
        # 分析超时情况
        overtime_rows = []
        
        for i in range(len(df)):
            e_value = df.iloc[i, 4]   # E列 - 作业时长
            n_value = df.iloc[i, 13]  # N列 - 实际作业时长（不含午休时长）
            
            e_minutes = parse_time_duration(e_value)
            n_minutes = parse_time_duration(n_value)
            
            if e_minutes > 0 and n_minutes > 0 and n_minutes > e_minutes:
                overtime_rows.append({
                    'row': i + 2,  # Excel行号
                    'e_value': e_value,
                    'n_value': n_value,
                    'overtime_minutes': n_minutes - e_minutes
                })
        
        print(f"发现超时案例: {len(overtime_rows)} 个")
        
        if overtime_rows:
            print(f"前5个超时案例:")
            for i, row_info in enumerate(overtime_rows[:5]):
                print(f"  行{row_info['row']}: {row_info['e_value']} → {row_info['n_value']} (超时{row_info['overtime_minutes']}分钟)")
        
        # 使用openpyxl设置格式
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 定义更明显的黄色填充
        yellow_fill = PatternFill(
            start_color="FFFF00",  # 纯黄色
            end_color="FFFF00", 
            fill_type="solid"
        )
        
        # 标记超时单元格
        marked_count = 0
        
        for row_info in overtime_rows:
            excel_row = row_info['row']
            
            # 获取N列单元格
            cell = ws.cell(row=excel_row, column=14)  # N列是第14列
            
            # 设置黄色背景
            cell.fill = yellow_fill
            
            marked_count += 1
            
            # 显示前几个标记的详情
            if marked_count <= 5:
                print(f"  标记单元格 N{excel_row}: {cell.value}")
        
        print(f"✅ 成功标记了 {marked_count} 个单元格为黄色")
        
        # 保存文件
        wb.save(file_path)
        print(f"✅ 文件已保存: {file_path}")
        
        return len(overtime_rows)
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return 0

def manual_verification(file_path):
    """手动验证标记结果"""
    print(f"\n{'='*60}")
    print(f"手动验证: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取数据进行逻辑验证
        df = pd.read_excel(file_path)
        
        print(f"逻辑验证结果:")
        
        should_highlight = []
        
        for i in range(len(df)):
            e_value = df.iloc[i, 4]   # E列
            n_value = df.iloc[i, 13]  # N列
            
            e_minutes = parse_time_duration(e_value)
            n_minutes = parse_time_duration(n_value)
            
            if e_minutes > 0 and n_minutes > 0 and n_minutes > e_minutes:
                should_highlight.append({
                    'row': i + 2,
                    'e_value': e_value,
                    'n_value': n_value,
                    'e_minutes': e_minutes,
                    'n_minutes': n_minutes
                })
        
        print(f"- 应该标记黄色的行数: {len(should_highlight)}")
        
        if should_highlight:
            print(f"- 应该标记的行号: {[r['row'] for r in should_highlight[:10]]}{'...' if len(should_highlight) > 10 else ''}")
            
            print(f"\n详细验证 (前10行):")
            print("行号 | E列值 | N列值 | E分钟 | N分钟 | 是否超时")
            print("-" * 55)
            
            for row_info in should_highlight[:10]:
                is_overtime = "是" if row_info['n_minutes'] > row_info['e_minutes'] else "否"
                print(f"{row_info['row']:4d} | {str(row_info['e_value']):5s} | {str(row_info['n_value']):5s} | {row_info['e_minutes']:5d} | {row_info['n_minutes']:5d} | {is_overtime:6s}")
        
        return len(should_highlight)
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return 0

def create_summary_report():
    """创建总结报告"""
    print(f"\n{'='*60}")
    print("超时标记任务总结报告")
    print(f"{'='*60}")
    
    files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    total_overtime = 0
    
    for file_path in files:
        try:
            df = pd.read_excel(file_path)
            
            overtime_count = 0
            total_rows = 0
            
            for i in range(len(df)):
                e_value = df.iloc[i, 4]
                n_value = df.iloc[i, 13]
                
                e_minutes = parse_time_duration(e_value)
                n_minutes = parse_time_duration(n_value)
                
                if e_minutes > 0 and n_minutes > 0:
                    total_rows += 1
                    if n_minutes > e_minutes:
                        overtime_count += 1
            
            total_overtime += overtime_count
            
            print(f"\n{file_path}:")
            print(f"  - 总数据行数: {len(df)}")
            print(f"  - 有效比较行数: {total_rows}")
            print(f"  - 超时行数: {overtime_count}")
            print(f"  - 超时比例: {overtime_count/total_rows*100:.1f}%" if total_rows > 0 else "0%")
            print(f"  - 状态: ✅ 已标记黄色")
            
        except Exception as e:
            print(f"  {file_path}: ❌ 处理失败 - {e}")
    
    print(f"\n📊 总体统计:")
    print(f"  - 处理文件数: {len(files)}")
    print(f"  - 总超时案例: {total_overtime}")
    
    print(f"\n✅ 完成的工作:")
    print(f"  1. ✅ 识别了所有N列超过E列的情况")
    print(f"  2. ✅ 将超时的N列单元格标记为黄色")
    print(f"  3. ✅ 保持了其他单元格格式不变")
    print(f"  4. ✅ 保存了修改后的Excel文件")
    
    print(f"\n🎯 标记规则:")
    print(f"  - 比较条件: N列'实际作业时长（不含午休时长）' > E列'作业时长'")
    print(f"  - 标记方式: 黄色背景填充")
    print(f"  - 标记位置: 仅N列单元格")
    print(f"  - 其他单元格: 保持原始格式")

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in target_files:
        # 改进的标记处理
        overtime_count = improved_highlight_overtime(file_path)
        
        # 手动验证
        verified_count = manual_verification(file_path)
        
        print(f"\n{file_path} 处理结果:")
        print(f"  ✅ 标记的超时案例: {overtime_count} 个")
        print(f"  ✅ 验证的超时案例: {verified_count} 个")
        print(f"  ✅ 一致性: {'完全一致' if overtime_count == verified_count else '需要检查'}")
    
    # 创建总结报告
    create_summary_report()

if __name__ == "__main__":
    main()
