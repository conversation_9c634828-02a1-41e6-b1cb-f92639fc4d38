import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from datetime import datetime, time
import warnings
warnings.filterwarnings('ignore')

def parse_time_duration(time_str):
    """解析时间时长字符串，返回总分钟数"""
    if pd.isna(time_str) or str(time_str).strip() == '' or str(time_str) == 'nan':
        return 0
    
    try:
        time_str = str(time_str).strip()
        
        # 如果是time对象
        if isinstance(time_str, time):
            return time_str.hour * 60 + time_str.minute
        
        # 如果是字符串格式 h:mm
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) >= 2:
                hours = int(parts[0])
                minutes = int(parts[1])
                return hours * 60 + minutes
        
        # 如果是纯数字，假设是分钟
        if time_str.replace('.', '').isdigit():
            return int(float(time_str))
        
        return 0
        
    except Exception as e:
        print(f"解析时间时长时出错 '{time_str}': {e}")
        return 0

def highlight_overtime_cells(file_path):
    """标记超时的单元格为黄色"""
    print(f"\n{'='*60}")
    print(f"处理文件: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 首先用pandas读取数据进行分析
        df = pd.read_excel(file_path)
        
        print(f"文件基本信息:")
        print(f"- 数据形状: {df.shape}")
        print(f"- 总行数: {len(df)}")
        
        # 找到E列和N列
        e_col_idx = 4   # E列 - 作业时长
        n_col_idx = 13  # N列 - 实际作业时长（不含午休时长）
        
        e_col_name = df.columns[e_col_idx] if e_col_idx < len(df.columns) else "未找到"
        n_col_name = df.columns[n_col_idx] if n_col_idx < len(df.columns) else "未找到"
        
        print(f"目标列信息:")
        print(f"- E列 (第{e_col_idx+1}列): {e_col_name}")
        print(f"- N列 (第{n_col_idx+1}列): {n_col_name}")
        
        # 分析需要标记的行
        overtime_rows = []
        total_compared = 0
        
        for i in range(len(df)):
            e_value = df.iloc[i, e_col_idx]  # 作业时长
            n_value = df.iloc[i, n_col_idx]  # 实际作业时长（不含午休时长）
            
            # 解析时间时长
            e_minutes = parse_time_duration(e_value)
            n_minutes = parse_time_duration(n_value)
            
            if e_minutes > 0 and n_minutes > 0:  # 只比较有效数据
                total_compared += 1
                
                if n_minutes > e_minutes:  # 实际时长超过标准时长
                    overtime_rows.append({
                        'row': i + 2,  # Excel行号（从1开始，加上标题行）
                        'e_value': e_value,
                        'n_value': n_value,
                        'e_minutes': e_minutes,
                        'n_minutes': n_minutes,
                        'overtime_minutes': n_minutes - e_minutes
                    })
        
        print(f"\n数据分析结果:")
        print(f"- 有效比较行数: {total_compared}")
        print(f"- 超时行数: {len(overtime_rows)}")
        print(f"- 超时比例: {len(overtime_rows)/total_compared*100:.1f}%" if total_compared > 0 else "0%")
        
        if overtime_rows:
            print(f"\n前10个超时案例:")
            print("行号 | 标准时长 | 实际时长 | 超时分钟")
            print("-" * 45)
            
            for i, row_info in enumerate(overtime_rows[:10]):
                print(f"{row_info['row']:4d} | {str(row_info['e_value']):8s} | {str(row_info['n_value']):8s} | {row_info['overtime_minutes']:8d}")
        
        # 使用openpyxl进行格式设置
        print(f"\n开始设置单元格颜色...")
        
        # 加载工作簿
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 定义黄色填充
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        
        # 标记超时的N列单元格
        marked_count = 0
        
        for row_info in overtime_rows:
            excel_row = row_info['row']
            excel_col = 'N'  # N列
            
            # 设置单元格背景色为黄色
            cell = ws[f'{excel_col}{excel_row}']
            cell.fill = yellow_fill
            marked_count += 1
        
        print(f"- 标记了 {marked_count} 个单元格为黄色")
        
        # 保存文件
        wb.save(file_path)
        print(f"✅ 文件已保存: {file_path}")
        
        return len(overtime_rows), total_compared
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return 0, 0

def verify_highlighting(file_path):
    """验证高亮设置"""
    print(f"\n{'='*60}")
    print(f"验证高亮设置: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取数据
        df = pd.read_excel(file_path)
        
        # 加载工作簿检查格式
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 检查N列的格式
        yellow_cells = []
        
        for row in range(2, len(df) + 2):  # 从第2行开始（跳过标题）
            cell = ws[f'N{row}']
            if cell.fill.start_color.rgb == 'FFFF00':  # 检查是否是黄色
                yellow_cells.append(row)
        
        print(f"验证结果:")
        print(f"- 检查的数据行数: {len(df)}")
        print(f"- 发现黄色标记的单元格: {len(yellow_cells)} 个")
        
        if yellow_cells:
            print(f"- 黄色标记的行号: {yellow_cells[:10]}{'...' if len(yellow_cells) > 10 else ''}")
        
        # 验证逻辑正确性
        correct_highlights = 0
        incorrect_highlights = 0
        
        for row in range(len(df)):
            excel_row = row + 2
            e_value = df.iloc[row, 4]   # E列
            n_value = df.iloc[row, 13]  # N列
            
            e_minutes = parse_time_duration(e_value)
            n_minutes = parse_time_duration(n_value)
            
            is_highlighted = excel_row in yellow_cells
            should_highlight = n_minutes > e_minutes and e_minutes > 0 and n_minutes > 0
            
            if is_highlighted == should_highlight:
                correct_highlights += 1
            else:
                incorrect_highlights += 1
        
        accuracy = correct_highlights / (correct_highlights + incorrect_highlights) * 100
        
        print(f"- 标记准确性: {accuracy:.1f}%")
        print(f"- 正确标记: {correct_highlights} 个")
        print(f"- 错误标记: {incorrect_highlights} 个")
        
        return len(yellow_cells)
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return 0

def main():
    """主函数"""
    target_files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    total_overtime_cases = 0
    total_compared_cases = 0
    
    for file_path in target_files:
        # 标记超时单元格
        overtime_count, compared_count = highlight_overtime_cells(file_path)
        total_overtime_cases += overtime_count
        total_compared_cases += compared_count
        
        # 验证标记结果
        highlighted_count = verify_highlighting(file_path)
        
        print(f"\n文件 {file_path} 处理完成:")
        print(f"  ✅ 超时案例: {overtime_count} 个")
        print(f"  ✅ 黄色标记: {highlighted_count} 个")
    
    # 总结
    print(f"\n{'='*60}")
    print("超时标记任务完成总结")
    print(f"{'='*60}")
    
    print(f"📊 处理统计:")
    print(f"  - 处理文件数: {len(target_files)} 个")
    print(f"  - 总比较行数: {total_compared_cases} 行")
    print(f"  - 总超时案例: {total_overtime_cases} 个")
    print(f"  - 超时比例: {total_overtime_cases/total_compared_cases*100:.1f}%" if total_compared_cases > 0 else "0%")
    
    print(f"\n✅ 完成的工作:")
    print(f"  1. 比较了N列与E列的时长数据")
    print(f"  2. 标记了所有N列超过E列的单元格为黄色")
    print(f"  3. 保持了其他单元格的原始格式")
    print(f"  4. 验证了标记的准确性")
    
    print(f"\n🎯 标记规则:")
    print(f"  - 条件: N列'实际作业时长（不含午休时长）' > E列'作业时长'")
    print(f"  - 标记: 黄色背景")
    print(f"  - 位置: 仅N列单元格")
    
    print(f"\n📝 文件状态:")
    for file_path in target_files:
        print(f"  ✅ {file_path}: 已完成标记")

if __name__ == "__main__":
    main()
