import pandas as pd

def final_verification():
    """最终验证原文件是否已正确更新"""
    files = [
        "作业登记表ABC栋202506.xlsx",
        "作业登记表ABC栋202507.xlsx"
    ]
    
    for file_path in files:
        print(f"\n{'='*60}")
        print(f"最终验证: {file_path}")
        print(f"{'='*60}")
        
        try:
            df = pd.read_excel(file_path)
            
            # 找到N列
            duration_col = None
            for i, col in enumerate(df.columns):
                if '实际作业时长（不含午休时长）' in str(col):
                    duration_col = i
                    break
            
            if duration_col is None:
                print("错误：找不到'实际作业时长（不含午休时长）'列")
                continue
            
            print(f"文件基本信息:")
            print(f"- 总行数: {len(df)}")
            print(f"- N列位置: 第{duration_col+1}列")
            
            # 检查N列数据
            n_col_data = df.iloc[:, duration_col]
            non_empty_count = n_col_data.notna().sum()
            
            print(f"- N列非空数据: {non_empty_count} 行")
            
            print(f"\nN列前10行数据:")
            for i in range(min(10, len(df))):
                value = df.iloc[i, duration_col]
                print(f"  行{i+1}: {value}")
            
            # 统计数据格式
            time_format_count = 0
            for i in range(len(df)):
                value = str(df.iloc[i, duration_col])
                if ':' in value and value != 'nan':
                    time_format_count += 1
            
            print(f"\n统计结果:")
            print(f"- 时间格式数据: {time_format_count} 行")
            print(f"- 数据完整性: {time_format_count/len(df)*100:.1f}%")
            
        except Exception as e:
            print(f"验证文件时出错: {e}")

if __name__ == "__main__":
    final_verification()
