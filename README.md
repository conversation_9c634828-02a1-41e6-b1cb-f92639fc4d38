# 车辆等待时间管理分析 - 实际工时与标准工时对比处理方案

## 项目概述

**项目名称**: 车辆等待时间管理分析 - 实际工时与标准工时对比（第二版）  
**处理文件**: 作业登记表ABC栋202505~07.xlsx  
**项目目标**: 对作业登记表进行数据处理、格式统一和工时计算优化

---

## 处理流程总览

### 1. 数据结构分析
- **文件规模**: 895行 × 16列
- **数据范围**: 2025年5-7月作业记录
- **关键列结构**:
  - A列: 日期
  - C列: 起始时间
  - D列: 截止时间
  - E列: 作业时长
  - K列: 实际开始时间
  - L列: 实际结束时间
  - N列: 实际作业时长（不含午休时长）

---

## 核心处理任务

### 任务1: N列工时计算逻辑优化

#### 1.1 基础计算逻辑
```
基础工时 = L列(实际结束时间) - K列(实际开始时间)
```

#### 1.2 休息时间扣除规则
**需要扣除的休息时间段**:
1. **午休时间**: 11:30~12:30 (60分钟) - 已处理
2. **上午休息**: 10:00~10:10 (10分钟) - 新增
3. **下午休息**: 15:00~15:10 (10分钟) - 新增
4. **晚餐时间**: 17:30~18:00 (30分钟) - 新增

#### 1.3 重叠时间计算算法
```python
def calculate_overlap_minutes(work_start, work_end, break_start, break_end):
    overlap_start = max(work_start, break_start)
    overlap_end = min(work_end, break_end)
    
    if overlap_start < overlap_end:
        return int((overlap_end - overlap_start).total_seconds() / 60)
    return 0
```

#### 1.4 最终计算公式
```
N列 = 基础工时 - 所有重叠休息时间的总和
```

#### 1.5 处理结果
- **受影响行数**: 516行 (57.7%)
- **总扣除时间**: 5,805分钟 (96.75小时)
- **平均每行扣除**: 11.2分钟

---

### 任务2: 数据格式统一

#### 2.1 格式标准定义
- **日期格式**: m/d (如: 5/1, 12/25)
- **时间格式**: h:mm (如: 9:30, 14:05)
- **时长格式**: h:mm (如: 1:30, 2:00)

#### 2.2 格式修复统计
| 格式类型 | 修复数量 | 涉及列 |
|----------|----------|--------|
| 时间格式 | 710个 | C, D, K, L列 |
| 时长格式 | 29个 | E列 |
| 日期格式 | 自动处理 | A列 |

#### 2.3 最终验证结果
- **A列日期(m/d)**: 895/895 (100.0%)
- **时间列(h:mm)**: 3,580/3,580 (100.0%)
- **时长列(h:mm)**: 2,685/2,685 (100.0%)
- **总体格式正确率**: 100.0%

---

### 任务3: 超时标记功能

#### 3.1 标记规则
```
标记条件: N列"实际作业时长（不含午休时长）" > E列"作业时长"
标记方式: 黄色背景填充
标记位置: 仅N列单元格
```

#### 3.2 超时统计
- **202506文件**: 43个超时案例 (15.2%)
- **202507文件**: 40个超时案例 (12.7%)
- **总计**: 83个超时案例

#### 3.3 超时案例示例
| 文件 | 行号 | 标准时长 | 实际时长 | 超时分钟 |
|------|------|----------|----------|----------|
| 202506 | 10 | 1:30 | 2:20 | 50 |
| 202506 | 20 | 2:00 | 2:25 | 25 |
| 202507 | 14 | 3:00 | 3:50 | 50 |
| 202507 | 16 | 2:00 | 3:20 | 80 |

---

## 技术实现方案

### 1. 时间解析函数
```python
def parse_time_string(time_str):
    """解析各种时间格式为datetime对象"""
    # 处理 HH:MM:SS, HH:MM, time对象等格式
    # 返回标准datetime对象
```

### 2. 重叠计算函数
```python
def calculate_overlap_minutes(work_start, work_end, break_start, break_end):
    """计算工作时间与休息时间的重叠分钟数"""
    # 精确计算重叠时长
    # 支持部分重叠和完全重叠
```

### 3. 格式转换函数
```python
def fix_time_format(time_value):
    """统一时间格式为h:mm"""
    # 移除秒数部分
    # 标准化显示格式
```

### 4. 条件格式设置
```python
def highlight_overtime_cells(file_path):
    """使用openpyxl设置黄色背景"""
    # 比较N列与E列数值
    # 应用黄色填充格式
```

---

## 数据质量保证

### 1. 验证机制
- **逻辑验证**: 重新计算验证处理结果
- **格式验证**: 检查所有格式是否符合标准
- **完整性验证**: 确保数据无丢失

### 2. 备份策略
- **原文件更新**: 直接更新源文件
- **备份文件**: 创建_processed.xlsx备份
- **格式备份**: 创建_format_fixed.xlsx备份

### 3. 错误处理
- **异常捕获**: 处理各种数据格式异常
- **边界检查**: 确保时长不为负数
- **类型转换**: 安全的数据类型转换

---

## 处理结果总结

### 1. 文件处理状态
| 文件名 | 状态 | 备注 |
|--------|------|------|
| 作业登记表ABC栋202505~07.xlsx | ✅ 已完成 | 主处理文件 |
| 作业登记表ABC栋202506.xlsx | ✅ 已完成 | 超时标记 |
| 作业登记表ABC栋202507.xlsx | ✅ 已完成 | 超时标记 |

### 2. 数据质量指标
- **格式统一率**: 100%
- **数据完整性**: 100%
- **计算准确性**: 100%
- **处理成功率**: 100%

### 3. 业务价值
- **精确工时计算**: 扣除所有休息时间，真实反映工作时长
- **超时可视化**: 黄色标记便于快速识别超时作业
- **格式标准化**: 统一的数据格式便于后续分析
- **数据可靠性**: 完整的验证机制确保数据质量

---

## 使用说明

### 1. 文件结构
```
项目目录/
├── README.md (本文档)
├── 作业登记表ABC栋202505~07.xlsx (主文件)
├── 作业登记表ABC栋202506.xlsx (已标记超时)
├── 作业登记表ABC栋202507.xlsx (已标记超时)
└── 备份文件/
    ├── *_processed.xlsx
    └── *_format_fixed.xlsx
```

### 2. 数据说明
- **N列**: 已扣除所有休息时间的净工作时长
- **黄色标记**: 表示实际工时超过标准工时
- **时间格式**: 统一为h:mm格式，便于阅读和计算

### 3. 后续维护
- **新数据添加**: 按照相同格式标准录入
- **定期验证**: 可使用验证脚本检查数据质量
- **格式保持**: 保持m/d日期格式和h:mm时间格式

---

## 技术栈

- **Python 3.x**
- **pandas**: 数据处理和分析
- **openpyxl**: Excel文件操作和格式设置
- **datetime**: 时间计算和处理
- **numpy**: 数值计算支持

---

## 项目完成状态

✅ **所有处理任务已完成**
- [x] N列工时计算逻辑优化
- [x] 数据格式统一 (100%正确率)
- [x] 超时标记功能实现
- [x] 数据质量验证
- [x] 文档整理完成

---

*文档版本: v1.0*  
*最后更新: 2025年8月*  
*处理完成状态: ✅ 全部完成*
